# RTSP Tools - 多传感器数据同步播放工具

[![Build Status](https://img.shields.io/badge/build-passing-brightgreen.svg)]()
[![License](https://img.shields.io/badge/license-MIT-blue.svg)]()
[![Version](https://img.shields.io/badge/version-2.0-orange.svg)]()
[![Tests](https://img.shields.io/badge/tests-passing-brightgreen.svg)]()
[![Memory Safe](https://img.shields.io/badge/memory-safe-brightgreen.svg)]()

一个专业的多传感器数据同步播放工具，支持视频流（RTSP）和传感器数据（UDP）的精确时序播放。

## ✨ 主要特性

- 🎥 **多路视频同步播放** - 支持多路RTSP视频流的精确时序播放
- 🔄 **多传感器数据融合** - 支持视频、雷达、激光雷达等多种传感器数据
- ⏱️ **高精度时序控制** - 保持2ms级别的时间戳精度
- 🔁 **循环播放模式** - 支持数据的循环重复播放
- 🔧 **向后兼容** - 100%兼容原有RTSP工具功能
- 🐳 **容器化部署** - 支持Docker容器化部署
- 📦 **便携式打包** - 支持创建独立运行的便携式包

## 🚀 快速开始

### 安装依赖

```bash
# Ubuntu/Debian
sudo apt-get update
sudo apt-get install -y build-essential pkg-config
sudo apt-get install -y libavformat-dev libavcodec-dev libavutil-dev libjsoncpp-dev

# CentOS/RHEL
sudo yum groupinstall -y "Development Tools"
sudo yum install -y ffmpeg-devel jsoncpp-devel

# macOS
brew install ffmpeg jsoncpp pkg-config
```

### 编译和运行

```bash
# 克隆项目
git clone <repository-url>
cd rtsp_tools

# 编译所有工具
make all

# 运行多传感器工具
./build/bin/multi_sensor_tool -f test/configs/test_config.txt -g json-only-test -d

# 提取视频时间戳
./build/bin/rtsp_timestamp_proc_tool test/data/video/sample.ts

# 剪辑视频片段（剪辑前2秒，毫秒时间戳）
./build/bin/video_clip_tool test/data/video/sample.ts output_clip.ts 1723169047372 1723169049372

# 剪辑视频片段（ISO时间格式）
./build/bin/video_clip_tool test/data/video/sample.ts output_clip.ts \
  "2024-08-09T10:04:07.372" "2024-08-09T10:04:17.372"
```

### Docker部署

```bash
# 构建Docker镜像
./scripts/build/build_docker.sh

# 运行容器
docker run --rm -p 8554:8554 -p 9001:9001/udp -p 9002:9002/udp rtsp-tools:latest
```

## 📁 项目结构

```
rtsp_tools/
├── src/                    # 源代码
│   ├── original/          # 原始RTSP工具
│   └── multi_sensor/      # 多传感器工具
├── docs/                   # 文档
│   ├── installation/      # 安装指南
│   ├── user_guides/       # 用户指南
│   ├── architecture/      # 架构文档
│   └── deployment/        # 部署指南
├── scripts/               # 构建和部署脚本
│   ├── build/            # 构建脚本
│   ├── test/             # 测试脚本
│   ├── deploy/           # 部署脚本
│   └── batch_clip_videos.sh  # 批量视频剪辑脚本
├── test/                  # 测试数据和配置
│   ├── configs/          # 测试配置文件
│   ├── data/             # 测试数据
│   └── scripts/          # 测试脚本
├── examples/              # 示例配置和数据
├── build/                 # 编译输出
└── release/              # 发布包
```

## 🛠️ 工具说明

| 工具名称 | 功能描述 | 主要用途 |
|---------|---------|---------|
| `multi_sensor_tool` | 多传感器数据同步播放工具 | 多种传感器数据融合播放 |
| `rtsp_tool` | 原始RTSP多路视频同步播放工具 | 兼容性播放，传统视频流 |
| `rtsp_timestamp_proc_tool` | 视频时间戳处理工具 | 从视频文件提取时间戳信息 |
| `video_clip_tool` | 视频剪辑工具 | 基于时间戳精确剪辑视频片段 |

## 📖 文档

- [完整编译指南](docs/COMPLETE_BUILD_GUIDE.md) - 详细的编译、打包和部署指南
- [使用示例](docs/USAGE_EXAMPLES.md) - 各种使用场景和配置示例
- [架构设计](docs/architecture/ARCHITECTURE_SIMPLE.md) - 系统架构和设计文档
- [多传感器指南](docs/user_guides/README_MULTI_SENSOR.md) - 多传感器功能使用指南
- [视频剪辑工具指南](docs/user_guides/VIDEO_CLIP_TOOL_GUIDE.md) - 视频剪辑工具使用指南
- [批量处理指南](docs/user_guides/BATCH_CLIP_GUIDE.md) - 批量视频剪辑脚本使用指南
- [项目结构](PROJECT_STRUCTURE.md) - 详细的项目结构说明

## 🔧 支持的传感器类型

| 传感器类型 | 数据格式 | 输出方式 | 配置示例 |
|-----------|---------|---------|---------|
| `video` | TS + 时间戳文件 | RTSP推流 | `video:camera.ts,camera.txt,rtsp://ip:port/stream` |
| `fisheye` | TS + 时间戳文件 | RTSP推流 | `fisheye:fisheye.ts,fisheye.txt,rtsp://ip:port/stream` |
| `radar` | JSON文件 | UDP数据包 | `radar:radar.json,udp://ip:port` |
| `lidar` | JSON文件 | UDP数据包 | `lidar:lidar.json,udp://ip:port` |

## 🎯 应用场景

- **自动驾驶测试** - 车载传感器数据的回放测试
- **智能监控** - 多路摄像头数据的同步播放
- **机器人开发** - 多传感器融合算法的数据回放测试
- **研发测试** - 传感器数据的标注、分析和算法验证
- **视频分析** - 基于时间戳的精确视频片段提取和分析
- **批量处理** - 大规模视频数据的自动化剪辑和处理

## 📊 性能指标

- **时序精度**: 2ms级别，符合工业级要求
- **内存使用**: 约600KB总分配，无内存泄漏
- **CPU使用**: 低CPU占用，高效运行
- **网络吞吐**: UDP数据包传输稳定
- **循环性能**: 每循环约100ms，时序稳定

## 🔨 构建选项

```bash
# 标准构建
make all

# 调试版本
make DEBUG=1 all

# 优化版本
make RELEASE=1 all

# 静态链接版本
make static

# 创建便携式包
make package
```

## 🐳 Docker支持

```bash
# 构建镜像
docker build -t rtsp-tools .

# 运行容器
docker run -d --name rtsp-tools \
  -p 8554:8554 \
  -p 9001:9001/udp \
  -p 9002:9002/udp \
  rtsp-tools

# 使用docker-compose
docker-compose up -d
```

## 🎬 视频剪辑工具使用示例

### 单个文件剪辑

```bash
# 1. 提取视频时间戳
./build/bin/rtsp_timestamp_proc_tool video.ts
# 生成 video.txt 包含时间戳信息

# 2. 查看时间戳范围
head -5 video.txt  # 查看前5个时间戳
tail -5 video.txt  # 查看后5个时间戳

# 3. 剪辑指定时间范围（毫秒时间戳）
./build/bin/video_clip_tool video.ts clip.ts 1723169047372 1723169050000
```

### 批量处理

```bash
# 批量剪辑目录下所有视频的前30秒
./scripts/batch_clip_videos.sh /path/to/videos /path/to/output 0:30

# 批量剪辑指定时间戳范围
./scripts/batch_clip_videos.sh /path/to/videos /path/to/output 1723169047372 1723169050000

# 批量剪辑（ISO时间格式，推荐）
./scripts/batch_clip_videos.sh /path/to/videos /path/to/output \
  "2024-08-09T10:04:10.000" "2024-08-09T10:04:20.000" --verbose

# 批量剪辑（从第5秒开始，持续10秒）
./scripts/batch_clip_videos.sh /path/to/videos /path/to/output +5:10 --verbose
```

### 批量处理脚本特性

- **🔄 自动遍历** - 递归扫描目录下所有 TS 文件
- **⏱️ 多种时间格式** - 支持毫秒时间戳、ISO时间格式、相对时长等
- **📁 智能输出** - 自动生成视频文件和对应的时间戳文件
- **🛡️ 错误处理** - 完善的异常处理和进度显示
- **⚡ 高效处理** - 支持跳过已存在文件，避免重复处理

### 批量处理选项

```bash
# 显示帮助信息
./scripts/batch_clip_videos.sh --help

# 详细输出模式
./scripts/batch_clip_videos.sh /videos /output 0:30 --verbose

# 自定义输出文件前缀
./scripts/batch_clip_videos.sh /videos /output 0:30 --prefix highlight_

# 强制覆盖已存在文件
./scripts/batch_clip_videos.sh /videos /output 0:30 --force

# 不生成时间戳文件
./scripts/batch_clip_videos.sh /videos /output 0:30 --no-timestamp
```

### 批量处理时间格式

```bash
# 毫秒时间戳 - 精确剪辑指定时间范围
./scripts/batch_clip_videos.sh /videos /output 1723169047372 1723169050000

# ISO时间格式 - 使用标准时间格式（推荐）
./scripts/batch_clip_videos.sh /videos /output "2024-08-09T10:04:10.000" "2024-08-09T10:04:20.000"

# 固定时长 - 剪辑每个视频的前N秒
./scripts/batch_clip_videos.sh /videos /output 0:30    # 前30秒
./scripts/batch_clip_videos.sh /videos /output 5:15   # 从第5秒开始，持续15秒

# 相对偏移 - 基于视频开始时间的相对剪辑
./scripts/batch_clip_videos.sh /videos /output +10:5  # 从第10秒开始，持续5秒
./scripts/batch_clip_videos.sh /videos /output +5 +15 # 从第5秒到第15秒
```

### 测试剪辑功能

```bash
# 单个文件剪辑测试
cd scripts/test
chmod +x test_video_clip.sh
./test_video_clip.sh

# 批量处理测试（时长模式）
./scripts/batch_clip_videos.sh ./test/data/video ./test/output 0:30 --verbose

# 批量处理测试（ISO时间格式）
./scripts/batch_clip_videos.sh ./test/data/video ./test/output \
  "2024-08-09T10:04:10.000" "2024-08-09T10:04:20.000" --verbose
```

## 🤝 贡献指南

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 🆘 技术支持

如遇到问题，请提供以下信息：
- 操作系统版本
- 编译器版本
- 依赖库版本
- 完整的错误日志
- 配置文件内容（脱敏后）

## 📞 联系方式

- 项目主页: [GitHub Repository]()
- 问题反馈: [Issues]()
- 文档: [Wiki]()

---

**版本**: v2.1  
**最后更新**: 2025-01-08  
**维护状态**: 积极维护中

### v2.1 更新内容
- ✨ 新增视频剪辑工具 `video_clip_tool`
- 🎯 支持基于 SEI 时间戳的精确视频剪辑
- 🔄 新增批量视频处理脚本 `batch_clip_videos.sh`
- 📁 支持目录遍历和批量剪辑，自动生成时间戳文件
- ⏰ 完善 ISO 时间格式支持，包含时区处理和多重解析策略
- 🛠️ 增强错误处理和调试功能，支持备用解析方案
- 📝 完善文档和使用示例
- 🧪 添加完整的测试脚本
