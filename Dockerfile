# Multi-stage Docker build for RTSP Tools
# 多阶段Docker构建，用于创建独立运行的RTSP工具容器

# 构建阶段
FROM ubuntu:22.04 AS builder

# 设置非交互模式
ENV DEBIAN_FRONTEND=noninteractive

# 安装构建依赖
RUN apt-get update && apt-get install -y \
    build-essential \
    pkg-config \
    cmake \
    git \
    libavformat-dev \
    libavcodec-dev \
    libavutil-dev \
    libavfilter-dev \
    libswscale-dev \
    libswresample-dev \
    libjsoncpp-dev \
    libpthread-stubs0-dev \
    zlib1g-dev \
    && rm -rf /var/lib/apt/lists/*

# 设置工作目录
WORKDIR /build

# 复制源代码
COPY . .

# 编译项目
RUN make clean && make RELEASE=1 all

# 运行时阶段
FROM ubuntu:22.04 AS runtime

# 设置非交互模式
ENV DEBIAN_FRONTEND=noninteractive

# 安装运行时依赖
RUN apt-get update && apt-get install -y \
    libavformat58 \
    libavcodec58 \
    libavutil56 \
    libavfilter7 \
    libswscale5 \
    libswresample3 \
    libjsoncpp25 \
    netcat-openbsd \
    curl \
    && rm -rf /var/lib/apt/lists/*

# 创建应用用户
RUN useradd -r -s /bin/false -d /opt/rtsp_tools rtsp_user

# 创建目录结构
RUN mkdir -p /opt/rtsp_tools/{bin,configs,data,logs} \
    && chown -R rtsp_user:rtsp_user /opt/rtsp_tools

# 复制编译好的程序
COPY --from=builder /build/build/bin/* /opt/rtsp_tools/bin/

# 复制配置文件和测试数据
COPY --from=builder /build/test/configs/* /opt/rtsp_tools/configs/
COPY --from=builder /build/test/data/* /opt/rtsp_tools/data/

# 复制文档
COPY --from=builder /build/COMPLETE_BUILD_GUIDE.md /opt/rtsp_tools/
COPY --from=builder /build/PROJECT_STRUCTURE.md /opt/rtsp_tools/

# 设置权限
RUN chmod +x /opt/rtsp_tools/bin/* \
    && chown -R rtsp_user:rtsp_user /opt/rtsp_tools

# 创建启动脚本
RUN echo '#!/bin/bash' > /opt/rtsp_tools/entrypoint.sh \
    && echo 'cd /opt/rtsp_tools' >> /opt/rtsp_tools/entrypoint.sh \
    && echo 'exec "$@"' >> /opt/rtsp_tools/entrypoint.sh \
    && chmod +x /opt/rtsp_tools/entrypoint.sh

# 设置工作目录
WORKDIR /opt/rtsp_tools

# 切换到应用用户
USER rtsp_user

# 暴露端口
EXPOSE **************

# 设置环境变量
ENV RTSP_LOG_LEVEL=INFO
ENV RTSP_TIMEOUT=30
ENV RTSP_MAX_RETRIES=3

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8554/ || exit 1

# 默认启动命令
ENTRYPOINT ["/opt/rtsp_tools/entrypoint.sh"]
CMD ["./bin/multi_sensor_tool", "-f", "configs/test_config.txt", "-g", "json-only-test", "-l", "-d"]
