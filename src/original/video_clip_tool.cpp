#include <cstdint>
#include <cstring>
#include <cstdlib>
#include <fstream>
#include <iostream>
#include <stdexcept>
#include <string>
#include <vector>
#include <sstream>
#include <iomanip>
#include <algorithm>
#include <regex>
#include <ctime>

extern "C"
{
#include <libavcodec/avcodec.h>
#include <libavformat/avformat.h>
#include <libavutil/time.h>
#include <libavutil/opt.h>
}

// 时间戳信息结构
struct TimestampInfo {
    int64_t timestamp_ms;  // 毫秒时间戳
    int64_t dts;          // 解码时间戳
    int64_t pts;          // 显示时间戳
    int frame_index;      // 帧索引
    bool is_keyframe;     // 是否为关键帧
};

// 从 SEI 数据中提取时间戳
int64_t extract_timestamp_from_sei(uint8_t* data, size_t size) {
    size_t pos = 0;
    
    while (pos < size) {
        if (pos + 3 < size && data[pos] == 0x00 && data[pos + 1] == 0x00 && data[pos + 2] == 0x01) {
            uint8_t nal_type = data[pos + 3] & 0x1F;
            size_t start_code_len = 3;
            
            if (nal_type == 6) { // SEI NALU
                size_t sei_payload_start = pos + start_code_len;
                size_t sei_payload_size = 0;
                
                // 找到 SEI 数据的结束位置
                while (sei_payload_start + sei_payload_size < size && 
                       data[sei_payload_start + sei_payload_size] != 0x80) {
                    sei_payload_size++;
                }
                
                if (sei_payload_start + sei_payload_size < size) {
                    // 假设时间戳从 SEI 数据的固定偏移开始
                    const size_t timestamp_offset = 20;
                    const size_t timestamp_size = 14;
                    
                    if (sei_payload_start + timestamp_offset + timestamp_size <= size) {
                        char timestamp[timestamp_size + 1] = {0};
                        memcpy(timestamp, data + sei_payload_start + timestamp_offset, timestamp_size);
                        timestamp[timestamp_size] = '\0';
                        
                        try {
                            int64_t timestamp_ms = std::stoll(timestamp);
                            if (timestamp_ms > 1000000000000) {
                                return timestamp_ms;
                            }
                        } catch (const std::exception &e) {
                            // 静默处理解析错误
                        }
                    }
                }
                
                // 跳过整个 SEI 数据块
                pos += start_code_len + sei_payload_size + 1;
                continue;
            }
        }
        pos++;
    }
    
    return -1; // 未找到有效时间戳
}

// 扫描视频文件，建立时间戳索引
bool scan_video_timestamps(const std::string& input_file, std::vector<TimestampInfo>& timestamps) {
    AVFormatContext* format_ctx = nullptr;
    
    if (avformat_open_input(&format_ctx, input_file.c_str(), nullptr, nullptr) < 0) {
        std::cerr << "无法打开输入文件: " << input_file << std::endl;
        return false;
    }
    
    if (avformat_find_stream_info(format_ctx, nullptr) < 0) {
        std::cerr << "无法获取流信息: " << input_file << std::endl;
        avformat_close_input(&format_ctx);
        return false;
    }
    
    int video_stream_index = -1;
    for (unsigned i = 0; i < format_ctx->nb_streams; ++i) {
        if (format_ctx->streams[i]->codecpar->codec_type == AVMEDIA_TYPE_VIDEO) {
            video_stream_index = i;
            break;
        }
    }
    
    if (video_stream_index == -1) {
        std::cerr << "未找到视频流" << std::endl;
        avformat_close_input(&format_ctx);
        return false;
    }
    
    AVPacket pkt;
    int frame_index = 0;
    
    std::cout << "正在扫描视频时间戳..." << std::endl;
    
    while (av_read_frame(format_ctx, &pkt) >= 0) {
        if (pkt.stream_index == video_stream_index && pkt.data && pkt.size > 0) {
            TimestampInfo info;
            info.dts = pkt.dts;
            info.pts = pkt.pts;
            info.frame_index = frame_index;
            info.is_keyframe = (pkt.flags & AV_PKT_FLAG_KEY) != 0;
            
            // 提取 SEI 时间戳
            int64_t sei_timestamp = extract_timestamp_from_sei(pkt.data, pkt.size);
            if (sei_timestamp > 0) {
                info.timestamp_ms = sei_timestamp;
                timestamps.push_back(info);
                
                if (timestamps.size() % 100 == 0) {
                    std::cout << "已扫描 " << timestamps.size() << " 个时间戳帧..." << std::endl;
                }
            }
            
            frame_index++;
        }
        
        av_packet_unref(&pkt);
    }
    
    avformat_close_input(&format_ctx);
    
    std::cout << "扫描完成，共找到 " << timestamps.size() << " 个时间戳帧" << std::endl;
    
    return !timestamps.empty();
}

// 剪辑视频
bool clip_video(const std::string& input_file, const std::string& output_file,
                int64_t start_time_ms, int64_t end_time_ms,
                const std::vector<TimestampInfo>& timestamps) {
    
    // 找到开始和结束的帧
    auto start_it = std::lower_bound(timestamps.begin(), timestamps.end(), start_time_ms,
        [](const TimestampInfo& info, int64_t time) {
            return info.timestamp_ms < time;
        });
    
    auto end_it = std::upper_bound(timestamps.begin(), timestamps.end(), end_time_ms,
        [](int64_t time, const TimestampInfo& info) {
            return time < info.timestamp_ms;
        });
    
    if (start_it == timestamps.end() || end_it == timestamps.begin()) {
        std::cerr << "指定的时间范围内没有数据" << std::endl;
        return false;
    }
    
    // 向前查找最近的关键帧
    while (start_it != timestamps.begin() && !start_it->is_keyframe) {
        --start_it;
    }
    
    int64_t start_dts = start_it->dts;
    int64_t end_dts = (end_it != timestamps.end()) ? end_it->dts : INT64_MAX;
    
    std::cout << "剪辑时间范围: " << start_time_ms << "ms - " << end_time_ms << "ms" << std::endl;
    std::cout << "实际时间范围: " << start_it->timestamp_ms << "ms - " 
              << ((end_it != timestamps.end()) ? std::to_string(end_it->timestamp_ms) : "结束") << "ms" << std::endl;
    
    // 打开输入文件
    AVFormatContext* input_ctx = nullptr;
    if (avformat_open_input(&input_ctx, input_file.c_str(), nullptr, nullptr) < 0) {
        std::cerr << "无法打开输入文件: " << input_file << std::endl;
        return false;
    }
    
    if (avformat_find_stream_info(input_ctx, nullptr) < 0) {
        std::cerr << "无法获取流信息" << std::endl;
        avformat_close_input(&input_ctx);
        return false;
    }
    
    // 创建输出文件
    AVFormatContext* output_ctx = nullptr;
    if (avformat_alloc_output_context2(&output_ctx, nullptr, nullptr, output_file.c_str()) < 0) {
        std::cerr << "无法创建输出上下文" << std::endl;
        avformat_close_input(&input_ctx);
        return false;
    }
    
    // 复制流信息
    for (unsigned i = 0; i < input_ctx->nb_streams; i++) {
        AVStream* in_stream = input_ctx->streams[i];
        AVStream* out_stream = avformat_new_stream(output_ctx, nullptr);
        
        if (!out_stream) {
            std::cerr << "无法创建输出流" << std::endl;
            avformat_close_input(&input_ctx);
            avformat_free_context(output_ctx);
            return false;
        }
        
        if (avcodec_parameters_copy(out_stream->codecpar, in_stream->codecpar) < 0) {
            std::cerr << "无法复制编解码器参数" << std::endl;
            avformat_close_input(&input_ctx);
            avformat_free_context(output_ctx);
            return false;
        }
        
        out_stream->codecpar->codec_tag = 0;
    }
    
    // 打开输出文件
    if (!(output_ctx->oformat->flags & AVFMT_NOFILE)) {
        if (avio_open(&output_ctx->pb, output_file.c_str(), AVIO_FLAG_WRITE) < 0) {
            std::cerr << "无法打开输出文件: " << output_file << std::endl;
            avformat_close_input(&input_ctx);
            avformat_free_context(output_ctx);
            return false;
        }
    }
    
    // 写入文件头
    if (avformat_write_header(output_ctx, nullptr) < 0) {
        std::cerr << "无法写入文件头" << std::endl;
        avformat_close_input(&input_ctx);
        if (!(output_ctx->oformat->flags & AVFMT_NOFILE)) {
            avio_closep(&output_ctx->pb);
        }
        avformat_free_context(output_ctx);
        return false;
    }
    
    // 复制数据包
    AVPacket pkt;
    int64_t first_dts = -1;
    int64_t dts_offset = 0;
    int packet_count = 0;
    
    std::cout << "开始剪辑视频..." << std::endl;
    
    while (av_read_frame(input_ctx, &pkt) >= 0) {
        AVStream* in_stream = input_ctx->streams[pkt.stream_index];
        AVStream* out_stream = output_ctx->streams[pkt.stream_index];
        
        // 只处理指定时间范围内的数据包
        if (in_stream->codecpar->codec_type == AVMEDIA_TYPE_VIDEO) {
            if (pkt.dts < start_dts) {
                av_packet_unref(&pkt);
                continue;
            }
            
            if (pkt.dts > end_dts) {
                av_packet_unref(&pkt);
                break;
            }
            
            // 记录第一个 DTS 作为偏移基准
            if (first_dts == -1) {
                first_dts = pkt.dts;
                dts_offset = first_dts;
            }
        }
        
        // 调整时间戳
        if (pkt.pts != AV_NOPTS_VALUE) {
            pkt.pts -= dts_offset;
        }
        if (pkt.dts != AV_NOPTS_VALUE) {
            pkt.dts -= dts_offset;
        }
        
        // 重新缩放时间戳
        av_packet_rescale_ts(&pkt, in_stream->time_base, out_stream->time_base);
        pkt.pos = -1;
        
        // 写入数据包
        if (av_interleaved_write_frame(output_ctx, &pkt) < 0) {
            std::cerr << "写入数据包失败" << std::endl;
            av_packet_unref(&pkt);
            break;
        }
        
        packet_count++;
        if (packet_count % 100 == 0) {
            std::cout << "已处理 " << packet_count << " 个数据包..." << std::endl;
        }
        
        av_packet_unref(&pkt);
    }
    
    // 写入文件尾
    av_write_trailer(output_ctx);
    
    // 清理资源
    avformat_close_input(&input_ctx);
    if (!(output_ctx->oformat->flags & AVFMT_NOFILE)) {
        avio_closep(&output_ctx->pb);
    }
    avformat_free_context(output_ctx);
    
    std::cout << "剪辑完成，共处理 " << packet_count << " 个数据包" << std::endl;
    
    return true;
}

// 解析时间字符串（支持多种格式）
int64_t parse_time_string(const std::string& time_str) {
    // 支持以下格式：
    // 1. 纯毫秒数: "1723169047372"
    // 2. ISO格式: "2024-08-09T10:30:47.372" 或 "2024-08-09T10:30:47"
    // 3. 简单格式: "10:30:47.372"
    
    // 尝试解析为纯数字（毫秒时间戳）
    try {
        int64_t timestamp = std::stoll(time_str);
        // 验证是否为合理的毫秒时间戳（2000年后到2100年前）
        if (timestamp > 946684800000LL && timestamp < 4102444800000LL) {
            return timestamp;
        }
    } catch (...) {
        // 不是纯数字，尝试其他格式
    }
    
    // 尝试解析 ISO 格式: YYYY-MM-DDTHH:MM:SS[.mmm][Z|+HH:MM|-HH:MM]
    // 支持多种ISO格式变体
    std::regex iso_regex(R"((\d{4})-(\d{2})-(\d{2})T(\d{2}):(\d{2}):(\d{2})(?:\.(\d{1,3}))?(?:Z|([+-]\d{2}):?(\d{2}))?)");;
    std::smatch iso_match;
    
    if (std::regex_match(time_str, iso_match, iso_regex)) {
        // 解析日期时间部分
        int year = std::stoi(iso_match[1]);
        int month = std::stoi(iso_match[2]);
        int day = std::stoi(iso_match[3]);
        int hour = std::stoi(iso_match[4]);
        int minute = std::stoi(iso_match[5]);
        int second = std::stoi(iso_match[6]);
        
        // 获取毫秒部分
        int milliseconds = 0;
        if (iso_match[7].matched) {
            std::string ms_str = iso_match[7].str();
            // 补齐到3位
            while (ms_str.length() < 3) ms_str += "0";
            milliseconds = std::stoi(ms_str.substr(0, 3));
        }
        
        // 处理时区
        int tz_offset_hours = 8;  // 默认UTC+8（中国时区）
        int tz_offset_minutes = 0;
        
        if (iso_match[8].matched) {
            // 有明确的时区信息
            tz_offset_hours = std::stoi(iso_match[8]);
            if (iso_match[9].matched) {
                tz_offset_minutes = std::stoi(iso_match[9]);
                if (tz_offset_hours < 0) {
                    tz_offset_minutes = -tz_offset_minutes;
                }
            }
        } else if (time_str.find('Z') != std::string::npos) {
            // UTC时间
            tz_offset_hours = 0;
            tz_offset_minutes = 0;
        }
        
        // 方法1：使用系统调用转换（更可靠）
        std::tm tm = {};
        tm.tm_year = year - 1900;
        tm.tm_mon = month - 1;
        tm.tm_mday = day;
        tm.tm_hour = hour;
        tm.tm_min = minute;
        tm.tm_sec = second;
        tm.tm_isdst = 0;
        
        // 临时设置时区为UTC进行计算
        char* old_tz = getenv("TZ");
        std::string saved_tz = old_tz ? old_tz : "";
        setenv("TZ", "UTC", 1);
        tzset();
        
        time_t utc_time = std::mktime(&tm);
        
        // 恢复时区
        if (!saved_tz.empty()) {
            setenv("TZ", saved_tz.c_str(), 1);
        } else {
            unsetenv("TZ");
        }
        tzset();
        
        if (utc_time == -1) {
            // 方法2：手动计算（备用方案）
            // 简化的日期转换，假设没有闰秒
            int64_t days = 0;
            
            // 计算从1970年1月1日到指定日期的天数
            for (int y = 1970; y < year; y++) {
                days += (y % 4 == 0 && (y % 100 != 0 || y % 400 == 0)) ? 366 : 365;
            }
            
            int month_days[] = {31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31};
            if (year % 4 == 0 && (year % 100 != 0 || year % 400 == 0)) {
                month_days[1] = 29;  // 闰年2月
            }
            
            for (int m = 1; m < month; m++) {
                days += month_days[m - 1];
            }
            days += day - 1;
            
            // 转换为秒，然后调整时区
            int64_t total_seconds = days * 86400LL + hour * 3600LL + minute * 60LL + second;
            total_seconds -= (tz_offset_hours * 3600 + tz_offset_minutes * 60);
            
            return total_seconds * 1000LL + milliseconds;
        }
        
        // 调整时区偏移
        utc_time -= (tz_offset_hours * 3600 + tz_offset_minutes * 60);
        
        int64_t result = static_cast<int64_t>(utc_time) * 1000 + milliseconds;
        
        // 验证结果是否合理
        if (result < 946684800000LL || result > 4102444800000LL) {
            std::cerr << "[WARNING] ISO时间解析结果异常: " << result << " ms，尝试备用方案" << std::endl;
            
            // 备用方案：使用系统命令date（仅Linux/macOS）
            std::string cmd = "date -d '" + time_str + "' +%s%3N 2>/dev/null";
            FILE* pipe = popen(cmd.c_str(), "r");
            if (pipe) {
                char buffer[128];
                if (fgets(buffer, sizeof(buffer), pipe)) {
                    pclose(pipe);
                    try {
                        result = std::stoll(buffer);
                        std::cerr << "[INFO] 使用系统date命令解析成功: " << result << " ms" << std::endl;
                        return result;
                    } catch (...) {}
                }
                pclose(pipe);
            }
        }
        
        return result;
    }
    
    // 尝试简单时间格式 HH:MM:SS[.mmm]
    std::regex time_regex(R"((\d{1,2}):(\d{2}):(\d{2})(?:\.(\d{3}))?)");;
    std::smatch time_match;
    
    if (std::regex_match(time_str, time_match, time_regex)) {
        int hours = std::stoi(time_match[1]);
        int minutes = std::stoi(time_match[2]);
        int seconds = std::stoi(time_match[3]);
        int milliseconds = 0;
        
        if (time_match[4].matched) {
            milliseconds = std::stoi(time_match[4]);
        }
        
        // 转换为相对毫秒数
        return (hours * 3600 + minutes * 60 + seconds) * 1000 + milliseconds;
    }
    
    std::cerr << "无法解析时间字符串: " << time_str << std::endl;
    std::cerr << "支持的格式：" << std::endl;
    std::cerr << "  1. 毫秒时间戳: 1723169047372" << std::endl;
    std::cerr << "  2. ISO格式: 2024-08-09T10:30:47.372" << std::endl;
    std::cerr << "  3. 时分秒格式: 10:30:47.372" << std::endl;
    return -1;
}

void print_usage(const char* program_name) {
    std::cout << "用法: " << program_name << " <输入视频文件> <输出视频文件> <开始时间> <结束时间>" << std::endl;
    std::cout << std::endl;
    std::cout << "时间格式支持：" << std::endl;
    std::cout << "  1. 毫秒时间戳: 1723169047372" << std::endl;
    std::cout << "  2. ISO格式: 2024-08-09T10:30:47.372" << std::endl;
    std::cout << "  3. 时分秒格式: 10:30:47.372" << std::endl;
    std::cout << std::endl;
    std::cout << "示例：" << std::endl;
    std::cout << "  " << program_name << " input.ts output.ts 1723169047372 1723169050000" << std::endl;
    std::cout << "  " << program_name << " input.ts output.ts \"2024-08-09T10:30:47.372\" \"2024-08-09T10:30:50.000\"" << std::endl;
}

int main(int argc, char* argv[]) {
    if (argc != 5) {
        print_usage(argv[0]);
        return -1;
    }
    
    std::string input_file = argv[1];
    std::string output_file = argv[2];
    std::string start_time_str = argv[3];
    std::string end_time_str = argv[4];
    
    // 解析时间参数
    int64_t start_time_ms = parse_time_string(start_time_str);
    int64_t end_time_ms = parse_time_string(end_time_str);
    
    if (start_time_ms < 0 || end_time_ms < 0) {
        std::cerr << "时间参数解析失败" << std::endl;
        std::cerr << "开始时间字符串: " << start_time_str << " -> " << start_time_ms << std::endl;
        std::cerr << "结束时间字符串: " << end_time_str << " -> " << end_time_ms << std::endl;
        
        // 如果解析失败，尝试使用系统date命令作为备用方案
        std::cerr << "尝试使用系统date命令解析..." << std::endl;
        
        std::string cmd1 = "date -d '" + start_time_str + "' +%s%3N 2>/dev/null";
        std::string cmd2 = "date -d '" + end_time_str + "' +%s%3N 2>/dev/null";
        
        FILE* pipe1 = popen(cmd1.c_str(), "r");
        FILE* pipe2 = popen(cmd2.c_str(), "r");
        
        if (pipe1 && pipe2) {
            char buffer1[128], buffer2[128];
            if (fgets(buffer1, sizeof(buffer1), pipe1) && fgets(buffer2, sizeof(buffer2), pipe2)) {
                try {
                    start_time_ms = std::stoll(buffer1);
                    end_time_ms = std::stoll(buffer2);
                    std::cerr << "系统date命令解析成功:" << std::endl;
                    std::cerr << "  开始时间: " << start_time_ms << " ms" << std::endl;
                    std::cerr << "  结束时间: " << end_time_ms << " ms" << std::endl;
                } catch (...) {
                    std::cerr << "系统date命令解析失败" << std::endl;
                }
            }
            if (pipe1) pclose(pipe1);
            if (pipe2) pclose(pipe2);
        }
        
        // 再次检查
        if (start_time_ms < 0 || end_time_ms < 0) {
            return -1;
        }
    }
    
    if (start_time_ms >= end_time_ms) {
        std::cerr << "开始时间必须小于结束时间" << std::endl;
        std::cerr << "开始时间: " << start_time_ms << " ms (" << start_time_str << ")" << std::endl;
        std::cerr << "结束时间: " << end_time_ms << " ms (" << end_time_str << ")" << std::endl;
        std::cerr << "时间差: " << (end_time_ms - start_time_ms) << " ms" << std::endl;
        return -1;
    }
    
    std::cout << "输入文件: " << input_file << std::endl;
    std::cout << "输出文件: " << output_file << std::endl;
    std::cout << "开始时间: " << start_time_ms << " ms" << std::endl;
    std::cout << "结束时间: " << end_time_ms << " ms" << std::endl;
    std::cout << "剪辑时长: " << (end_time_ms - start_time_ms) / 1000.0 << " 秒" << std::endl;
    std::cout << std::endl;
    
    // 扫描视频时间戳
    std::vector<TimestampInfo> timestamps;
    if (!scan_video_timestamps(input_file, timestamps)) {
        std::cerr << "扫描视频时间戳失败" << std::endl;
        return -1;
    }
    
    // 显示时间戳范围
    if (!timestamps.empty()) {
        std::cout << "视频时间戳范围: " << timestamps.front().timestamp_ms 
                  << " ms - " << timestamps.back().timestamp_ms << " ms" << std::endl;
        std::cout << std::endl;
    }
    
    // 执行剪辑
    if (clip_video(input_file, output_file, start_time_ms, end_time_ms, timestamps)) {
        std::cout << "视频剪辑成功！输出文件: " << output_file << std::endl;
        return 0;
    } else {
        std::cerr << "视频剪辑失败" << std::endl;
        return -1;
    }
}
