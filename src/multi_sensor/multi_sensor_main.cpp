#include "multi_sensor_server.h"
#include <signal.h>
#include <iostream>

// 全局变量用于信号处理
SimpleMultiSensorManager* g_manager = nullptr;

void signalHandler(int signal) {
    if (g_manager) {
        std::cout << "\nReceived signal " << signal << ", stopping playback..." << std::endl;
        g_manager->stopPlayback();
    }
    exit(0);
}

void printUsage(const char* program_name) {
    std::cout << "Multi-Sensor Data Synchronous Playback Tool v1.0" << std::endl;
    std::cout << "Usage: " << program_name << " -f <config_file> -g <group_name> [options]" << std::endl;
    std::cout << std::endl;
    std::cout << "Required options:" << std::endl;
    std::cout << "  -f <config_file>  INI format configuration file" << std::endl;
    std::cout << "  -g <group_name>   Sensor group name to play" << std::endl;
    std::cout << std::endl;
    std::cout << "Optional options:" << std::endl;
    std::cout << "  -d               Enable debug output" << std::endl;
    std::cout << "  -l               Enable loop playback mode" << std::endl;
    std::cout << "  -t <type>        Set video SEI frame type (0: default, 1: Dahua, 2: Hikvision)" << std::endl;
    std::cout << "  -h               Show this help message" << std::endl;
    std::cout << std::endl;
    std::cout << "Examples:" << std::endl;
    std::cout << "  # Play multi-sensor scene with loop and debug" << std::endl;
    std::cout << "  " << program_name << " -f config.txt -g scene-001 -l -d" << std::endl;
    std::cout << std::endl;
    std::cout << "  # Play legacy video-only scene" << std::endl;
    std::cout << "  " << program_name << " -f rtspconfig.txt -g deqing-001 -l" << std::endl;
    std::cout << std::endl;
    std::cout << "  # Play radar/lidar scene" << std::endl;
    std::cout << "  " << program_name << " -f sensors.txt -g radar-test -d" << std::endl;
    std::cout << std::endl;
    std::cout << "Supported sensor types:" << std::endl;
    std::cout << "  - video: Regular video streams (TS format)" << std::endl;
    std::cout << "  - fisheye: Fisheye video streams (TS format)" << std::endl;
    std::cout << "  - radar: Radar data (JSON format)" << std::endl;
    std::cout << "  - lidar: Lidar data (JSON format)" << std::endl;
    std::cout << std::endl;
    std::cout << "Configuration file format:" << std::endl;
    std::cout << "  [group-name]" << std::endl;
    std::cout << "  video:camera1.ts,camera1.txt,rtsp://192.168.1.100:8554/stream1" << std::endl;
    std::cout << "  radar:radar.json,udp://192.168.1.100:9001" << std::endl;
    std::cout << "  lidar:lidar.json,udp://192.168.1.100:9002" << std::endl;
}

int main(int argc, char* argv[]) {
    std::string config_file;
    std::string group_name;
    bool is_loop = false;
    bool is_debug = false;
    uint16_t camera_type = 0;
    
    // 解析命令行参数
    for (int i = 1; i < argc; i++) {
        std::string arg = argv[i];
        
        if (arg == "-f" && i + 1 < argc) {
            config_file = argv[++i];
        } else if (arg == "-g" && i + 1 < argc) {
            group_name = argv[++i];
        } else if (arg == "-l") {
            is_loop = true;
        } else if (arg == "-d") {
            is_debug = true;
        } else if (arg == "-t" && i + 1 < argc) {
            camera_type = static_cast<uint16_t>(std::stoi(argv[++i]));
        } else if (arg == "-h") {
            printUsage(argv[0]);
            return 0;
        } else {
            std::cerr << "Unknown option: " << arg << std::endl;
            printUsage(argv[0]);
            return 1;
        }
    }
    
    // 检查必需参数
    if (config_file.empty() || group_name.empty()) {
        std::cerr << "Error: Missing required parameters" << std::endl;
        std::cerr << "Both -f (config file) and -g (group name) are required" << std::endl;
        std::cout << std::endl;
        printUsage(argv[0]);
        return 1;
    }
    
    // 设置信号处理
    signal(SIGINT, signalHandler);
    signal(SIGTERM, signalHandler);
#ifndef _WIN32
    signal(SIGQUIT, signalHandler);
#endif
    
    try {
        // 创建多传感器管理器
        SimpleMultiSensorManager manager(is_loop, is_debug, camera_type);
        g_manager = &manager;
        
        // 显示启动信息
        std::cout << "Multi-Sensor Data Synchronous Playback Tool v1.0" << std::endl;
        std::cout << "Configuration: " << config_file << std::endl;
        std::cout << "Group: " << group_name << std::endl;
        std::cout << "Loop mode: " << (is_loop ? "enabled" : "disabled") << std::endl;
        std::cout << "Debug mode: " << (is_debug ? "enabled" : "disabled") << std::endl;
        std::cout << "Camera type: " << camera_type << std::endl;
        std::cout << "Press Ctrl+C to stop..." << std::endl;
        std::cout << std::endl;
        
        // 加载配置
        if (!manager.loadConfiguration(config_file, group_name)) {
            SimpleErrorHandler::logError(SimpleErrorHandler::FATAL, 
                "Failed to load configuration from " + config_file + ", group: " + group_name);
            return 1;
        }
        
        // 初始化所有传感器
        if (!manager.initializeAllSensors()) {
            SimpleErrorHandler::logError(SimpleErrorHandler::WARNING, 
                "Some sensors failed to initialize, continuing with available sensors");
        }
        
        // 开始播放
        manager.startPlayback();
        
    } catch (const std::exception& e) {
        SimpleErrorHandler::logError(SimpleErrorHandler::FATAL, 
            "Exception in main: " + std::string(e.what()));
        return 1;
    }
    
    std::cout << "Playback completed successfully" << std::endl;
    return 0;
}

// 测试用的配置文件生成函数
void generateTestConfig() {
    std::ofstream config("test_config.txt");
    config << "# Multi-sensor test configuration\n";
    config << "# This is a test configuration file\n";
    config << "\n";
    config << "[test-scene]\n";
    config << "# Video sensors\n";
    config << "video:test_video.ts,test_video.txt,rtsp://localhost:8554/stream1\n";
    config << "fisheye:test_fisheye.ts,test_fisheye.txt,rtsp://localhost:8554/stream2\n";
    config << "\n";
    config << "# JSON sensors\n";
    config << "radar:test_radar.json,udp://127.0.0.1:9001\n";
    config << "lidar:test_lidar.json,udp://127.0.0.1:9002\n";
    config << "\n";
    config << "[legacy-test]\n";
    config << "# Legacy format (backward compatibility)\n";
    config << "old_video.ts,old_video.txt,rtsp://localhost:8554/legacy\n";
    config.close();
    
    std::cout << "Generated test_config.txt" << std::endl;
}

// 测试用的JSON数据生成函数
void generateTestJsonData() {
    // 生成测试雷达数据
    std::ofstream radar("test_radar.json");
    radar << "[\n";
    for (int i = 0; i < 100; i++) {
        int64_t timestamp = 1723169047000 + i * 40; // 40ms间隔
        radar << "  {\n";
        radar << "    \"timestamp\": " << timestamp << ",\n";
        radar << "    \"data\": {\n";
        radar << "      \"sensor_id\": \"radar_001\",\n";
        radar << "      \"detection_count\": " << (i % 5 + 1) << ",\n";
        radar << "      \"detections\": [\n";
        radar << "        {\"range\": " << (25.0 + i * 0.1) << ", \"angle\": " << (i % 360) << ", \"velocity\": " << (10.0 + i * 0.05) << "}\n";
        radar << "      ]\n";
        radar << "    }\n";
        radar << "  }";
        if (i < 99) radar << ",";
        radar << "\n";
    }
    radar << "]\n";
    radar.close();
    
    // 生成测试激光雷达数据
    std::ofstream lidar("test_lidar.json");
    lidar << "[\n";
    for (int i = 0; i < 100; i++) {
        int64_t timestamp = 1723169047000 + i * 50; // 50ms间隔
        lidar << "  {\n";
        lidar << "    \"timestamp\": " << timestamp << ",\n";
        lidar << "    \"data\": {\n";
        lidar << "      \"sensor_id\": \"lidar_001\",\n";
        lidar << "      \"point_count\": " << (1000 + i * 10) << ",\n";
        lidar << "      \"points\": [\n";
        lidar << "        {\"x\": " << (i * 0.1) << ", \"y\": " << (i * 0.2) << ", \"z\": " << (i * 0.05) << ", \"intensity\": " << (100 + i) << "}\n";
        lidar << "      ]\n";
        lidar << "    }\n";
        lidar << "  }";
        if (i < 99) lidar << ",";
        lidar << "\n";
    }
    lidar << "]\n";
    lidar.close();
    
    std::cout << "Generated test_radar.json and test_lidar.json" << std::endl;
}

// 简单的测试函数
void runBasicTests() {
    std::cout << "Running basic tests..." << std::endl;
    
    // 测试配置解析器
    SimpleConfigParser parser;
    generateTestConfig();
    
    auto configs = parser.parseGroup("test_config.txt", "test-scene");
    std::cout << "Parsed " << configs.size() << " sensor configurations" << std::endl;
    
    for (const auto& config : configs) {
        std::cout << "  Type: " << config.type << ", File: " << config.data_file 
                  << ", Output: " << config.output_address;
        if (config.output_port > 0) {
            std::cout << ":" << config.output_port;
        }
        std::cout << std::endl;
    }
    
    // 生成测试数据
    generateTestJsonData();
    
    std::cout << "Basic tests completed" << std::endl;
}
