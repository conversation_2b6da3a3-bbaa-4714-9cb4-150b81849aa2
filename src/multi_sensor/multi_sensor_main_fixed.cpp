// 修复后的main文件关键部分

#include "multi_sensor_server_fixed.h"
#include <signal.h>
#include <iostream>
#include <atomic>
#include <memory>
#include <thread>
#include <chrono>
#include <unistd.h>
#include <cstring>

// 使用原子变量确保线程安全
std::atomic<bool> g_should_stop(false);
SimpleMultiSensorManager* g_manager = nullptr;

// 修复8: 安全的信号处理器
void signalHandler(int /*signal*/) {
    // 只设置标志，不执行复杂操作
    g_should_stop.store(true);

    // 可选：输出简单信息（注意：cout不是异步信号安全的，生产环境应避免）
    const char* msg = "\nSignal received, stopping...\n";
#ifndef _WIN32
    (void)write(STDERR_FILENO, msg, strlen(msg));  // 忽略返回值警告
#else
    // Windows 下使用 WriteFile 或简单忽略此输出
    (void)msg; // 避免未使用变量警告
#endif
}

// 打印使用说明
void printUsage(const char* program_name) {
    std::cout << "Multi-Sensor Data Synchronous Playback Tool v1.0 (Fixed)" << std::endl;
    std::cout << "Usage: " << program_name << " [options]" << std::endl;
    std::cout << std::endl;
    std::cout << "Required options:" << std::endl;
    std::cout << "  -f <config_file>    Configuration file path" << std::endl;
    std::cout << "  -g <group_name>     Configuration group name" << std::endl;
    std::cout << std::endl;
    std::cout << "Optional options:" << std::endl;
    std::cout << "  -l                  Enable loop playback mode" << std::endl;
    std::cout << "  -d                  Enable debug output" << std::endl;
    std::cout << "  -t <camera_type>    Camera type (0=normal, 1=fisheye)" << std::endl;
    std::cout << "  -h                  Show this help message" << std::endl;
    std::cout << std::endl;
    std::cout << "Examples:" << std::endl;
    std::cout << "  " << program_name << " -f config.txt -g scene-001 -l -d" << std::endl;
    std::cout << "  " << program_name << " -f test_config.txt -g json-only-test -d" << std::endl;
}

// 修复9: 改进的主函数
int main(int argc, char* argv[]) {
    std::string config_file;
    std::string group_name;
    bool is_loop = false;
    bool is_debug = false;
    uint16_t camera_type = 0;
    
    // 解析命令行参数（保持原有逻辑）
    for (int i = 1; i < argc; i++) {
        std::string arg = argv[i];
        
        if (arg == "-f" && i + 1 < argc) {
            config_file = argv[++i];
        } else if (arg == "-g" && i + 1 < argc) {
            group_name = argv[++i];
        } else if (arg == "-l") {
            is_loop = true;
        } else if (arg == "-d") {
            is_debug = true;
        } else if (arg == "-t" && i + 1 < argc) {
            camera_type = static_cast<uint16_t>(std::stoi(argv[++i]));
        } else if (arg == "-h") {
            printUsage(argv[0]);
            return 0;
        } else {
            std::cerr << "Unknown option: " << arg << std::endl;
            printUsage(argv[0]);
            return 1;
        }
    }
    
    // 检查必需参数
    if (config_file.empty() || group_name.empty()) {
        std::cerr << "Error: Missing required parameters" << std::endl;
        std::cerr << "Both -f (config file) and -g (group name) are required" << std::endl;
        std::cout << std::endl;
        printUsage(argv[0]);
        return 1;
    }
    
    // 设置信号处理
    signal(SIGINT, signalHandler);
    signal(SIGTERM, signalHandler);
#ifndef _WIN32
    signal(SIGQUIT, signalHandler);
#endif
    
    int exit_code = 0;
    
    try {
        // 创建多传感器管理器（C++11兼容）
        std::unique_ptr<SimpleMultiSensorManager> manager(new SimpleMultiSensorManager(is_loop, is_debug, camera_type));
        g_manager = manager.get();
        
        // 显示启动信息
        std::cout << "Multi-Sensor Data Synchronous Playback Tool v1.0" << std::endl;
        std::cout << "Configuration: " << config_file << std::endl;
        std::cout << "Group: " << group_name << std::endl;
        std::cout << "Loop mode: " << (is_loop ? "enabled" : "disabled") << std::endl;
        std::cout << "Debug mode: " << (is_debug ? "enabled" : "disabled") << std::endl;
        std::cout << "Camera type: " << camera_type << std::endl;
        std::cout << "Press Ctrl+C to stop..." << std::endl;
        std::cout << std::endl;
        
        // 加载配置
        if (!manager->loadConfiguration(config_file, group_name)) {
            SimpleErrorHandler::logError(SimpleErrorHandler::ERROR, 
                "Failed to load configuration from " + config_file + ", group: " + group_name);
            return 1;
        }
        
        // 初始化所有传感器
        if (!manager->initializeAllSensors()) {
            SimpleErrorHandler::logError(SimpleErrorHandler::WARNING, 
                "Some sensors failed to initialize, continuing with available sensors");
        }
        
        // 启动播放线程
        std::thread playback_thread([&]() {
            manager->startPlayback();
        });
        
        // 主线程监控停止信号
        while (!g_should_stop.load()) {
            std::this_thread::sleep_for(std::chrono::milliseconds(100));
        }
        
        // 停止播放
        std::cout << "\nStopping playback..." << std::endl;
        manager->stopPlayback();
        
        // 等待播放线程结束
        if (playback_thread.joinable()) {
            playback_thread.join();
        }
        
        // 清理资源（智能指针会自动处理）
        g_manager = nullptr;
        
    } catch (const std::exception& e) {
        SimpleErrorHandler::logError(SimpleErrorHandler::ERROR, 
            "Exception in main: " + std::string(e.what()));
        exit_code = 1;
    }
    
    std::cout << "Playback " << (exit_code == 0 ? "completed successfully" : "failed") << std::endl;
    return exit_code;
}
