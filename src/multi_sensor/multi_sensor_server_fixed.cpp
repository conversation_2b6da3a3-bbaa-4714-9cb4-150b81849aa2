// 这是修复后的关键函数，可以替换原有实现中的对应函数

// 修复1: VideoSensor析构函数 - 添加更严格的状态检查
VideoSensor::~VideoSensor() {
    // 先检查状态，避免空指针访问
    if (stream_context_.output_ctx) {
        // 安全地写入trailer
        int ret = av_write_trailer(stream_context_.output_ctx);
        if (ret < 0) {
            char errbuf[AV_ERROR_MAX_STRING_SIZE];
            av_strerror(ret, errbuf, sizeof(errbuf));
            SimpleErrorHandler::logError(SimpleErrorHandler::WARNING,
                "Failed to write trailer: " + std::string(errbuf));
        }
        
        // 检查pb是否存在再关闭
        if (stream_context_.output_ctx->pb && 
            !(stream_context_.output_ctx->oformat->flags & AVFMT_NOFILE)) {
            avio_closep(&stream_context_.output_ctx->pb);
        }
        avformat_free_context(stream_context_.output_ctx);
        stream_context_.output_ctx = nullptr;
    }
    
    if (stream_context_.input_ctx) {
        avformat_close_input(&stream_context_.input_ctx);
        stream_context_.input_ctx = nullptr;
    }
}

// 修复2: 添加流索引边界检查的handle_timestamps函数
void VideoSensor::handle_timestamps() {
    // 添加边界检查
    if (stream_context_.pkt.stream_index < 0 || 
        stream_context_.pkt.stream_index >= static_cast<int>(stream_context_.last_dts.size())) {
        SimpleErrorHandler::logError(SimpleErrorHandler::WARNING,
            "Invalid stream index: " + std::to_string(stream_context_.pkt.stream_index));
        return;
    }
    
    AVStream *in_stream = stream_context_.input_ctx->streams[stream_context_.pkt.stream_index];
    AVStream *out_stream = stream_context_.output_ctx->streams[stream_context_.pkt.stream_index];

    if (is_loop_) {
        if (stream_context_.last_dts[stream_context_.pkt.stream_index] == 0) {
            stream_context_.last_dts[stream_context_.pkt.stream_index] = stream_context_.pkt.dts;
        } else {
            stream_context_.pkt.dts = stream_context_.last_dts[stream_context_.pkt.stream_index] + stream_context_.average_interval;
        }
    }

    stream_context_.pkt.pts = stream_context_.pkt.dts;

    // 确保时间戳单调递增
    if (stream_context_.pkt.dts <= stream_context_.last_dts[stream_context_.pkt.stream_index]) {
        stream_context_.pkt.dts = stream_context_.last_dts[stream_context_.pkt.stream_index] + 1;
        stream_context_.pkt.pts = stream_context_.pkt.dts;
    }

    stream_context_.last_dts[stream_context_.pkt.stream_index] = stream_context_.pkt.dts;
    av_packet_rescale_ts(&stream_context_.pkt, in_stream->time_base, out_stream->time_base);
}

// 修复3: 修正时间同步逻辑
bool SimpleMultiSensorManager::shouldProcessSensor(SensorBase* sensor, int64_t current_time, int64_t time_interval) {
    int64_t sensor_timestamp = sensor->getCurrentTimestamp();
    if (sensor_timestamp == INT64_MAX) {
        return false; // 传感器没有更多数据
    }
    
    // 修正时间比较逻辑
    // 当前经过的时间 >= 传感器帧应该播放的时间
    int64_t elapsed_time = current_time - earliest_timestamp_;
    int64_t sensor_relative_time = sensor_timestamp - earliest_timestamp_;
    
    return elapsed_time >= sensor_relative_time;
}

// 修复4: 循环播放时重新计算时间基准
void SimpleMultiSensorManager::resetAllSensors() {
    for (auto& sensor : sensors_) {
        sensor->reset();
    }
    
    // 重新计算最早时间戳
    findEarliestTimestamp();
    
    SimpleErrorHandler::logError(SimpleErrorHandler::INFO,
        "All sensors reset, new earliest timestamp: " + std::to_string(earliest_timestamp_));
}

// 修复5: 改进的播放循环
void SimpleMultiSensorManager::playbackLoop() {
    auto start_time = get_timestamp_ms();
    int64_t base_time = earliest_timestamp_;
    
    while (running_) {
        auto current_time = get_timestamp_ms();
        int64_t elapsed_time = current_time - start_time;
        
        bool any_sensor_has_data = false;
        
        // 遍历所有传感器
        for (auto& sensor : sensors_) {
            if (!sensor->hasMoreData()) {
                continue;
            }
            
            any_sensor_has_data = true;
            
            // 获取传感器当前时间戳
            int64_t sensor_timestamp = sensor->getCurrentTimestamp();
            if (sensor_timestamp == INT64_MAX) {
                continue;
            }
            
            // 计算相对时间
            int64_t sensor_relative_time = sensor_timestamp - base_time;
            
            // 如果到了播放时间
            if (elapsed_time >= sensor_relative_time) {
                if (!sensor->processFrame(current_time)) {
                    if (is_debug_) {
                        SimpleErrorHandler::logError(SimpleErrorHandler::WARNING,
                            "Failed to process frame for sensor: " + sensor->getDataFile());
                    }
                }
            }
        }
        
        // 检查是否所有传感器都播放完毕
        if (!any_sensor_has_data) {
            if (!is_loop_) {
                SimpleErrorHandler::logError(SimpleErrorHandler::INFO,
                    "All sensors finished, exiting");
                break;
            }
            
            // 循环播放：重置所有传感器
            SimpleErrorHandler::logError(SimpleErrorHandler::INFO,
                "All sensors finished, restarting loop");
            resetAllSensors();
            
            // 重置时间基准
            start_time = get_timestamp_ms();
            base_time = earliest_timestamp_;
        }
        
        // 保持原有的休眠间隔
        std::this_thread::sleep_for(std::chrono::milliseconds(2));
    }
    
    SimpleErrorHandler::logError(SimpleErrorHandler::INFO, "Playback loop ended");
}

// 修复6: UDP发送添加重试机制
bool SimpleUDPSender::sendData(const std::string& data) {
    if (!initialized_) {
        if (!initialize()) {
            return false;
        }
    }
    
    const int MAX_RETRIES = 3;
    const int RETRY_DELAY_MS = 10;
    
    for (int retry = 0; retry < MAX_RETRIES; ++retry) {
        ssize_t sent = sendto(socket_fd_, data.c_str(), data.length(), 0,
                             (struct sockaddr*)&server_addr_, sizeof(server_addr_));
        
        if (sent >= 0) {
            return true;  // 发送成功
        }
        
        // 发送失败，等待后重试
        if (retry < MAX_RETRIES - 1) {
            std::this_thread::sleep_for(std::chrono::milliseconds(RETRY_DELAY_MS));
        }
    }
    
    // 所有重试都失败
    SimpleErrorHandler::logError(SimpleErrorHandler::WARNING, 
        "Failed to send UDP data after " + std::to_string(MAX_RETRIES) + 
        " retries to " + target_address_ + ":" + std::to_string(target_port_));
    return false;
}

// 修复7: 安全的init_stream函数
int VideoSensor::init_stream() {
    // 使用RAII模式管理资源
    struct StreamGuard {
        AVFormatContext** input_ctx;
        AVFormatContext** output_ctx;
        bool committed = false;
        
        ~StreamGuard() {
            if (!committed) {
                if (*output_ctx) {
                    if ((*output_ctx)->pb && 
                        !((*output_ctx)->oformat->flags & AVFMT_NOFILE)) {
                        avio_closep(&(*output_ctx)->pb);
                    }
                    avformat_free_context(*output_ctx);
                    *output_ctx = nullptr;
                }
                if (*input_ctx) {
                    avformat_close_input(input_ctx);
                    *input_ctx = nullptr;
                }
            }
        }
    } guard{&stream_context_.input_ctx, &stream_context_.output_ctx};
    
    // 1. 打开输入文件
    if (avformat_open_input(&stream_context_.input_ctx, 
                            stream_context_.input_file_ts.c_str(), 
                            nullptr, nullptr) < 0) {
        SimpleErrorHandler::logError(SimpleErrorHandler::ERROR,
            "Cannot open input file: " + stream_context_.input_file_ts);
        return -1;
    }

    // 2. 获取流信息
    if (avformat_find_stream_info(stream_context_.input_ctx, nullptr) < 0) {
        SimpleErrorHandler::logError(SimpleErrorHandler::ERROR,
            "Cannot find stream info: " + stream_context_.input_file_ts);
        return -1;
    }

    // 3. 创建输出上下文
    if (avformat_alloc_output_context2(&stream_context_.output_ctx, nullptr, 
                                       "rtsp", stream_context_.output_url.c_str()) < 0) {
        SimpleErrorHandler::logError(SimpleErrorHandler::ERROR,
            "Cannot create output context: " + stream_context_.output_url);
        return -1;
    }

    // 4. 复制流参数
    for (unsigned i = 0; i < stream_context_.input_ctx->nb_streams; i++) {
        AVStream *out_stream = avformat_new_stream(stream_context_.output_ctx, nullptr);
        if (!out_stream) {
            SimpleErrorHandler::logError(SimpleErrorHandler::ERROR,
                "Failed to create output stream");
            return -1;
        }
        
        AVStream *in_stream = stream_context_.input_ctx->streams[i];
        
        if (avcodec_parameters_copy(out_stream->codecpar, in_stream->codecpar) < 0) {
            SimpleErrorHandler::logError(SimpleErrorHandler::ERROR,
                "Failed to copy codec parameters");
            return -1;
        }
        
        out_stream->time_base = in_stream->time_base;

        if (in_stream->codecpar->codec_type == AVMEDIA_TYPE_VIDEO) {
            AVRational avg_frame_rate = in_stream->avg_frame_rate;
            double fps = av_q2d(avg_frame_rate);
            if (fps <= 0.0) {
                double fallback = av_q2d(in_stream->r_frame_rate);
                fps = (fallback > 0.0) ? fallback : 25.0;
            }
            stream_context_.average_interval = static_cast<int64_t>((1000.0 / fps) * 90.0);
        }
    }

    // 5. 建立网络连接
    if (!(stream_context_.output_ctx->oformat->flags & AVFMT_NOFILE)) {
        if (avio_open(&stream_context_.output_ctx->pb, 
                     stream_context_.output_url.c_str(), AVIO_FLAG_WRITE) < 0) {
            SimpleErrorHandler::logError(SimpleErrorHandler::ERROR,
                "Cannot open output URL: " + stream_context_.output_url);
            return -1;
        }
    }

    // 6. 写入流头部
    if (avformat_write_header(stream_context_.output_ctx, nullptr) < 0) {
        SimpleErrorHandler::logError(SimpleErrorHandler::ERROR,
            "Cannot write header to: " + stream_context_.output_url);
        return -1;
    }

    // 按实际流数量初始化 last_dts，避免越界
    stream_context_.last_dts.assign(stream_context_.input_ctx->nb_streams, 0);
    
    // 成功，不要清理资源
    guard.committed = true;
    return 0;
}
