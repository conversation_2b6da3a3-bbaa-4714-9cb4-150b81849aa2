# Comprehensive Multi-Sensor Test Configuration File
# 全面的多传感器测试配置文件

[basic-json-test]
# 基础JSON传感器测试
radar:test/data/json/radar_test.json,udp://127.0.0.1:9001
lidar:test/data/json/lidar_test.json,udp://127.0.0.1:9002

[stress-json-test]
# 压力测试 - 多个相同类型传感器
radar:test/data/json/radar_test.json,udp://127.0.0.1:9001
radar:test/data/json/radar_test.json,udp://127.0.0.1:9003
radar:test/data/json/radar_test.json,udp://127.0.0.1:9005
lidar:test/data/json/lidar_test.json,udp://127.0.0.1:9002
lidar:test/data/json/lidar_test.json,udp://127.0.0.1:9004
lidar:test/data/json/lidar_test.json,udp://127.0.0.1:9006

[error-test-missing-file]
# 错误测试 - 缺失文件
radar:test/data/json/nonexistent.json,udp://127.0.0.1:9001

[error-test-invalid-port]
# 错误测试 - 无效端口
radar:test/data/json/radar_test.json,udp://127.0.0.1:99999

[empty-test]
# 空配置测试

[single-sensor-test]
# 单传感器测试
radar:test/data/json/radar_test.json,udp://127.0.0.1:9001

[timing-test]
# 时序测试 - 用于测试时间同步
radar:test/data/json/radar_test.json,udp://127.0.0.1:9001
lidar:test/data/json/lidar_test.json,udp://127.0.0.1:9002

[loop-test]
# 循环播放测试
radar:test/data/json/radar_test.json,udp://127.0.0.1:9001

[network-test]
# 网络测试 - 不同IP地址
radar:test/data/json/radar_test.json,udp://127.0.0.1:9001
lidar:test/data/json/lidar_test.json,udp://*************:9002
