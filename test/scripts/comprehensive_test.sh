#!/bin/bash

# Comprehensive Multi-Sensor Test Script
# 全面的多传感器测试脚本 - 对比原版和修复版

set -e  # Exit on any error

echo "=== Comprehensive Multi-Sensor Test Script ==="
echo "Testing both original and fixed versions"

# 获取脚本目录和项目根目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
ROOT_DIR="$(cd "${SCRIPT_DIR}/../.." && pwd)"
BIN_DIR="${ROOT_DIR}/build/bin"
ORIGINAL_TOOL="${BIN_DIR}/multi_sensor_tool"
FIXED_TOOL="${BIN_DIR}/multi_sensor_tool_fixed"
CONFIG_FILE="${ROOT_DIR}/test/configs/comprehensive_test_config.txt"
TEST_LOG_DIR="${ROOT_DIR}/test/output/test_logs"

# 创建日志目录
mkdir -p "${TEST_LOG_DIR}"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查工具是否存在
check_tools() {
    log_info "Checking if tools exist..."
    
    if [ ! -f "${ORIGINAL_TOOL}" ]; then
        log_error "Original tool not found: ${ORIGINAL_TOOL}"
        log_info "Building original tool..."
        make -C "${ROOT_DIR}" multi-sensor || {
            log_error "Failed to build original tool"
            exit 1
        }
    fi
    
    if [ ! -f "${FIXED_TOOL}" ]; then
        log_error "Fixed tool not found: ${FIXED_TOOL}"
        log_info "Building fixed tool..."
        make -C "${ROOT_DIR}" multi-sensor-fixed || {
            log_error "Failed to build fixed tool"
            exit 1
        }
    fi
    
    log_success "Both tools are available"
}

# 启动UDP监听器
start_udp_listeners() {
    log_info "Starting UDP listeners..."
    
    # 启动多个UDP监听器
    for port in 9001 9002 9003 9004 9005 9006; do
        (
            nc -u -l ${port} 2>/dev/null | while read line; do
                echo "[UDP:${port}] $(date '+%H:%M:%S.%3N'): $line"
            done
        ) > "${TEST_LOG_DIR}/udp_${port}.log" 2>&1 &
        
        UDP_PIDS+=($!)
    done
    
    # 等待监听器启动
    sleep 2
    log_success "UDP listeners started on ports 9001-9006"
}

# 停止UDP监听器
stop_udp_listeners() {
    log_info "Stopping UDP listeners..."
    for pid in "${UDP_PIDS[@]}"; do
        kill $pid 2>/dev/null || true
    done
    wait 2>/dev/null || true
    log_success "UDP listeners stopped"
}

# 运行单个测试
run_test() {
    local tool_name="$1"
    local tool_path="$2"
    local test_name="$3"
    local config_group="$4"
    local timeout_duration="$5"
    local expected_result="$6"  # "success" or "failure"
    
    log_info "Running test: ${test_name} with ${tool_name}"
    
    local log_file="${TEST_LOG_DIR}/${tool_name}_${test_name}.log"
    local result="UNKNOWN"
    
    # 运行测试
    timeout ${timeout_duration}s "${tool_path}" -f "${CONFIG_FILE}" -g "${config_group}" -d > "${log_file}" 2>&1 &
    local test_pid=$!
    
    # 等待测试完成或超时
    if wait $test_pid 2>/dev/null; then
        local exit_code=$?
        if [ $exit_code -eq 0 ]; then
            result="SUCCESS"
        else
            result="FAILURE"
        fi
    else
        # 超时或被杀死
        kill $test_pid 2>/dev/null || true
        result="TIMEOUT"
    fi
    
    # 检查结果是否符合预期
    if [ "$expected_result" = "success" ] && [ "$result" = "SUCCESS" ]; then
        log_success "${tool_name} - ${test_name}: PASSED (${result})"
        return 0
    elif [ "$expected_result" = "failure" ] && [ "$result" = "FAILURE" ]; then
        log_success "${tool_name} - ${test_name}: PASSED (Expected failure: ${result})"
        return 0
    elif [ "$expected_result" = "timeout" ] && [ "$result" = "TIMEOUT" ]; then
        log_success "${tool_name} - ${test_name}: PASSED (Expected timeout: ${result})"
        return 0
    else
        log_error "${tool_name} - ${test_name}: FAILED (Expected: ${expected_result}, Got: ${result})"
        return 1
    fi
}

# 主测试函数
run_all_tests() {
    local tool_name="$1"
    local tool_path="$2"
    local passed=0
    local failed=0
    
    log_info "Starting tests for ${tool_name}"
    
    # 测试用例定义: test_name config_group timeout expected_result
    local tests=(
        "basic_json_test basic-json-test 10 timeout"
        "single_sensor_test single-sensor-test 8 timeout"
        "stress_test stress-json-test 15 timeout"
        "error_missing_file error-test-missing-file 5 failure"
        "error_invalid_port error-test-invalid-port 5 failure"
        "empty_config empty-test 3 failure"
        "timing_test timing-test 10 timeout"
    )
    
    for test_def in "${tests[@]}"; do
        read -r test_name config_group timeout expected <<< "$test_def"
        
        if run_test "${tool_name}" "${tool_path}" "${test_name}" "${config_group}" "${timeout}" "${expected}"; then
            ((passed++))
        else
            ((failed++))
        fi
        
        # 测试间隔
        sleep 2
    done
    
    log_info "${tool_name} Test Summary: ${passed} passed, ${failed} failed"
    return $failed
}

# 清理函数
cleanup() {
    log_info "Cleaning up..."
    stop_udp_listeners
    
    # 杀死可能残留的测试进程
    pkill -f multi_sensor_tool 2>/dev/null || true
    
    log_success "Cleanup completed"
}

# 设置信号处理
trap cleanup EXIT INT TERM

# 全局变量
UDP_PIDS=()

# 主执行流程
main() {
    log_info "Starting comprehensive multi-sensor testing..."
    
    # 检查工具
    check_tools
    
    # 启动UDP监听器
    start_udp_listeners
    
    # 测试原版工具
    log_info "=== Testing Original Version ==="
    run_all_tests "original" "${ORIGINAL_TOOL}"
    local original_failures=$?
    
    # 等待一下
    sleep 3
    
    # 测试修复版工具
    log_info "=== Testing Fixed Version ==="
    run_all_tests "fixed" "${FIXED_TOOL}"
    local fixed_failures=$?
    
    # 总结
    echo ""
    log_info "=== Test Summary ==="
    log_info "Original version failures: ${original_failures}"
    log_info "Fixed version failures: ${fixed_failures}"
    
    if [ $fixed_failures -lt $original_failures ]; then
        log_success "Fixed version shows improvement!"
    elif [ $fixed_failures -eq $original_failures ]; then
        log_warning "Both versions have similar results"
    else
        log_warning "Fixed version has more failures than original"
    fi
    
    log_info "Test logs are available in: ${TEST_LOG_DIR}"
    log_info "UDP logs are available in: ${TEST_LOG_DIR}/udp_*.log"
    
    return $(($original_failures + $fixed_failures))
}

# 运行主函数
main "$@"
