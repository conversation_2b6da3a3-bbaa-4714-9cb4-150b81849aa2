#!/bin/bash

# Side-by-side comparison test for original vs fixed multi-sensor tools
# 并排比较测试：原版 vs 修复版多传感器工具

set -e

echo "=== Multi-Sensor Comparison Test ==="
echo "Testing original vs fixed versions side-by-side"

# 获取脚本目录和项目根目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
ROOT_DIR="$(cd "${SCRIPT_DIR}/../.." && pwd)"
BIN_DIR="${ROOT_DIR}/build/bin"
ORIGINAL_TOOL="${BIN_DIR}/multi_sensor_tool"
FIXED_TOOL="${BIN_DIR}/multi_sensor_tool_fixed"
CONFIG_FILE="${ROOT_DIR}/test/configs/test_config.txt"
TEST_LOG_DIR="${ROOT_DIR}/test/output/comparison_logs"

# 创建日志目录
mkdir -p "${TEST_LOG_DIR}"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 启动UDP监听器
start_udp_listeners() {
    log_info "Starting UDP listeners for comparison..."
    
    # 启动监听器用于原版工具
    (
        nc -u -l 9001 2>/dev/null | while read line; do
            echo "[ORIGINAL:9001] $(date '+%H:%M:%S.%3N'): $line"
        done
    ) > "${TEST_LOG_DIR}/original_udp_9001.log" 2>&1 &
    ORIGINAL_UDP1_PID=$!
    
    (
        nc -u -l 9002 2>/dev/null | while read line; do
            echo "[ORIGINAL:9002] $(date '+%H:%M:%S.%3N'): $line"
        done
    ) > "${TEST_LOG_DIR}/original_udp_9002.log" 2>&1 &
    ORIGINAL_UDP2_PID=$!
    
    # 启动监听器用于修复版工具
    (
        nc -u -l 9003 2>/dev/null | while read line; do
            echo "[FIXED:9003] $(date '+%H:%M:%S.%3N'): $line"
        done
    ) > "${TEST_LOG_DIR}/fixed_udp_9003.log" 2>&1 &
    FIXED_UDP1_PID=$!
    
    (
        nc -u -l 9004 2>/dev/null | while read line; do
            echo "[FIXED:9004] $(date '+%H:%M:%S.%3N'): $line"
        done
    ) > "${TEST_LOG_DIR}/fixed_udp_9004.log" 2>&1 &
    FIXED_UDP2_PID=$!
    
    # 等待监听器启动
    sleep 2
    log_success "UDP listeners started"
}

# 停止UDP监听器
stop_udp_listeners() {
    log_info "Stopping UDP listeners..."
    for pid in $ORIGINAL_UDP1_PID $ORIGINAL_UDP2_PID $FIXED_UDP1_PID $FIXED_UDP2_PID; do
        kill $pid 2>/dev/null || true
    done
    wait 2>/dev/null || true
    log_success "UDP listeners stopped"
}

# 创建比较配置文件
create_comparison_config() {
    local config_file="${ROOT_DIR}/test/configs/comparison_config.txt"
    
    cat > "$config_file" << 'EOF'
# Comparison test configuration
# 比较测试配置

[original-test]
# 原版工具测试配置
radar:test/data/json/radar_test.json,udp://127.0.0.1:9001
lidar:test/data/json/lidar_test.json,udp://127.0.0.1:9002

[fixed-test]
# 修复版工具测试配置
radar:test/data/json/radar_test.json,udp://127.0.0.1:9003
lidar:test/data/json/lidar_test.json,udp://127.0.0.1:9004
EOF
    
    echo "$config_file"
}

# 运行比较测试
run_comparison_test() {
    local test_duration="$1"
    local config_file="$2"
    
    log_info "Running ${test_duration}s comparison test..."
    
    # 启动原版工具
    log_info "Starting original tool..."
    timeout ${test_duration}s "${ORIGINAL_TOOL}" -f "$config_file" -g original-test -d > "${TEST_LOG_DIR}/original_output.log" 2>&1 &
    local original_pid=$!
    
    # 启动修复版工具
    log_info "Starting fixed tool..."
    timeout ${test_duration}s "${FIXED_TOOL}" -f "$config_file" -g fixed-test -d > "${TEST_LOG_DIR}/fixed_output.log" 2>&1 &
    local fixed_pid=$!
    
    # 等待两个工具完成
    wait $original_pid 2>/dev/null || true
    local original_exit_code=$?
    
    wait $fixed_pid 2>/dev/null || true
    local fixed_exit_code=$?
    
    log_info "Original tool exit code: $original_exit_code"
    log_info "Fixed tool exit code: $fixed_exit_code"
    
    return 0
}

# 分析结果
analyze_results() {
    log_info "Analyzing comparison results..."
    
    # 统计UDP数据包数量
    local original_packets_9001=$(wc -l < "${TEST_LOG_DIR}/original_udp_9001.log" 2>/dev/null || echo "0")
    local original_packets_9002=$(wc -l < "${TEST_LOG_DIR}/original_udp_9002.log" 2>/dev/null || echo "0")
    local fixed_packets_9003=$(wc -l < "${TEST_LOG_DIR}/fixed_udp_9003.log" 2>/dev/null || echo "0")
    local fixed_packets_9004=$(wc -l < "${TEST_LOG_DIR}/fixed_udp_9004.log" 2>/dev/null || echo "0")
    
    local original_total=$((original_packets_9001 + original_packets_9002))
    local fixed_total=$((fixed_packets_9003 + fixed_packets_9004))
    
    echo ""
    log_info "=== Comparison Results ==="
    log_info "Original tool UDP packets sent:"
    log_info "  Port 9001 (radar): $original_packets_9001"
    log_info "  Port 9002 (lidar): $original_packets_9002"
    log_info "  Total: $original_total"
    
    log_info "Fixed tool UDP packets sent:"
    log_info "  Port 9003 (radar): $fixed_packets_9003"
    log_info "  Port 9004 (lidar): $fixed_packets_9004"
    log_info "  Total: $fixed_total"
    
    # 比较数据包数量
    if [ $original_total -eq $fixed_total ]; then
        log_success "Both versions sent the same number of packets: $original_total"
    elif [ $fixed_total -gt $original_total ]; then
        log_warning "Fixed version sent more packets: $fixed_total vs $original_total"
    else
        log_warning "Original version sent more packets: $original_total vs $fixed_total"
    fi
    
    # 检查日志文件大小
    local original_log_size=$(wc -l < "${TEST_LOG_DIR}/original_output.log" 2>/dev/null || echo "0")
    local fixed_log_size=$(wc -l < "${TEST_LOG_DIR}/fixed_output.log" 2>/dev/null || echo "0")
    
    log_info "Log file sizes:"
    log_info "  Original: $original_log_size lines"
    log_info "  Fixed: $fixed_log_size lines"
    
    # 显示最后几行日志以检查差异
    echo ""
    log_info "=== Original Tool Last 5 Lines ==="
    tail -5 "${TEST_LOG_DIR}/original_output.log" 2>/dev/null || echo "No output"
    
    echo ""
    log_info "=== Fixed Tool Last 5 Lines ==="
    tail -5 "${TEST_LOG_DIR}/fixed_output.log" 2>/dev/null || echo "No output"
}

# 清理函数
cleanup() {
    log_info "Cleaning up..."
    stop_udp_listeners
    
    # 杀死可能残留的测试进程
    pkill -f multi_sensor_tool 2>/dev/null || true
    
    log_success "Cleanup completed"
}

# 设置信号处理
trap cleanup EXIT INT TERM

# 全局变量
ORIGINAL_UDP1_PID=""
ORIGINAL_UDP2_PID=""
FIXED_UDP1_PID=""
FIXED_UDP2_PID=""

# 主执行流程
main() {
    log_info "Starting side-by-side comparison test..."
    
    # 创建比较配置文件
    local config_file=$(create_comparison_config)
    log_info "Created comparison config: $config_file"
    
    # 启动UDP监听器
    start_udp_listeners
    
    # 运行比较测试
    run_comparison_test 10 "$config_file"
    
    # 等待一下让UDP数据完全接收
    sleep 2
    
    # 分析结果
    analyze_results
    
    log_success "Comparison test completed!"
    log_info "Detailed logs available in: ${TEST_LOG_DIR}"
    
    return 0
}

# 运行主函数
main "$@"
