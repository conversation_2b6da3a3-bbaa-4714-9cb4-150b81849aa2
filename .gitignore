# RTSP Tools .gitignore

# 编译输出
build/
*.o
*.obj
*.exe
*.dll
*.so
*.dylib
*.a
*.lib

# 临时文件
*.tmp
*.temp
*~
.DS_Store
Thumbs.db

# IDE和编辑器文件
.vscode/
.idea/
*.swp
*.swo
*~

# 日志文件
*.log
logs/
debug.log
error.log

# 测试输出
test_output/
*.test
coverage.out

# 发布包
release/
*.tar.gz
*.zip
*.deb
*.rpm
rtsp_tools_portable_*

# Docker相关
.dockerignore
docker-compose.override.yml

# 系统文件
*.Zone.Identifier
.Trashes
ehthumbs.db

# 数据文件（大文件）
data/videos/*.ts
data/videos/*.mp4
data/videos/*.avi
data/timestamps/*.txt

# 配置文件（包含敏感信息）
config/production.conf
config/secrets.conf

# 备份文件
*.bak
*.backup
*.orig

# 性能分析文件
*.prof
perf.data*
callgrind.out.*

# 内存调试文件
vgcore.*
core.*

# 编译器生成的文件
*.d
*.dep
.depend

# CMake生成的文件
CMakeCache.txt
CMakeFiles/
cmake_install.cmake
Makefile.cmake

# 自动生成的文档
docs/html/
docs/latex/
docs/man/

# 包管理器文件
node_modules/
vendor/
.vendor/

# 环境变量文件
.env
.env.local
.env.production

# 临时构建目录
tmp/
temp/
