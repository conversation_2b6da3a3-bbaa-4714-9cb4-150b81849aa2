# Windows环境快速开始指南

## 🚀 Windows环境下的编译和运行

### 📋 前提条件

1. **安装MSYS2环境**（推荐）
   - 下载: https://www.msys2.org/
   - 安装后打开MSYS2终端

2. **安装编译工具和依赖库**
   ```bash
   # 在MSYS2终端中执行
   pacman -Syu
   pacman -S mingw-w64-x86_64-gcc
   pacman -S mingw-w64-x86_64-make
   pacman -S mingw-w64-x86_64-pkg-config
   pacman -S mingw-w64-x86_64-ffmpeg
   pacman -S mingw-w64-x86_64-jsoncpp
   ```

3. **设置环境变量**
   - 将 `C:\msys64\mingw64\bin` 添加到系统PATH

## 🔧 编译方法

### 方法1: 使用Windows批处理脚本（推荐）

#### 编译原始RTSP工具
```cmd
# 在项目根目录下执行
scripts\build_original.bat
```

#### 编译多传感器工具
```cmd
# 在项目根目录下执行
scripts\build_multi_sensor.bat
```

### 方法2: 使用PowerShell/命令提示符手动编译

#### 编译原始RTSP工具
```cmd
# 创建输出目录
mkdir build\bin

# 编译RTSP服务器
g++ -std=c++11 -O2 -g -Wall -Wextra ^
    -o build\bin\rtsp_tool.exe ^
    src\original\rtsp_server.cpp ^
    -lavformat -lavcodec -lavutil -lpthread -lws2_32

# 编译时间戳处理工具
g++ -std=c++11 -O2 -g -Wall -Wextra ^
    -o build\bin\rtsp_timestamp_proc_tool.exe ^
    src\original\rtsp_timestamp_proc_tool.cpp ^
    -lavformat -lavcodec -lavutil -lpthread -lws2_32
```

#### 编译多传感器工具
```cmd
# 编译多传感器工具
g++ -std=c++11 -O2 -g -Wall -Wextra ^
    -o build\bin\multi_sensor_tool.exe ^
    src\multi_sensor\multi_sensor_main.cpp src\multi_sensor\multi_sensor_server.cpp ^
    -lavformat -lavcodec -lavutil -ljsoncpp -lpthread -lws2_32
```

## 🏃 运行方法

### 原始RTSP工具
```cmd
# 播放视频流
build\bin\rtsp_tool.exe -f test\configs\rtspconfig.txt -g deqing-001 -l -d

# 处理时间戳
build\bin\rtsp_timestamp_proc_tool.exe input_video.ts output_timestamps.txt
```

### 多传感器工具
```cmd
# 播放JSON传感器数据
build\bin\multi_sensor_tool.exe -f test\configs\test_config.txt -g json-only-test -d

# 向后兼容模式
build\bin\multi_sensor_tool.exe -f test\configs\rtspconfig.txt -g deqing-001 -l -d

# 显示帮助
build\bin\multi_sensor_tool.exe -h
```

## 🧪 测试运行

### 1. 生成测试数据
```cmd
# 运行测试数据生成脚本
scripts\generate_test_data.bat
```

### 2. 启动UDP监听器（测试JSON传感器）
```powershell
# 在PowerShell中启动UDP监听器
.\scripts\udp_listener.ps1 -Port 9001
```

### 3. 运行多传感器工具
```cmd
# 在另一个命令提示符窗口中
build\bin\multi_sensor_tool.exe -f test\configs\test_config.txt -g json-only-test -d
```

## 📁 Windows环境下的文件路径

### 配置文件路径
- 原始工具配置: `test\configs\rtspconfig.txt`
- 多传感器配置: `test\configs\test_config.txt`

### 测试数据路径
- JSON数据: `test\data\json\*.json`
- 视频数据: `test\data\video\*.ts` 和 `*.txt`

### 可执行文件路径
- 原始RTSP工具: `build\bin\rtsp_tool.exe`
- 时间戳工具: `build\bin\rtsp_timestamp_proc_tool.exe`
- 多传感器工具: `build\bin\multi_sensor_tool.exe`

## 🐛 Windows环境常见问题

### 1. 编译器未找到
```
错误: 'g++' 不是内部或外部命令
解决: 安装MSYS2并将mingw64\bin添加到PATH
```

### 2. 依赖库未找到
```
错误: fatal error: libavformat/avformat.h: No such file or directory
解决: pacman -S mingw-w64-x86_64-ffmpeg
```

### 3. pkg-config错误
```
错误: 'pkg-config' 不是内部或外部命令
解决: pacman -S mingw-w64-x86_64-pkg-config
```

### 4. 网络端口被占用
```
错误: Failed to bind to UDP port
解决: netstat -an | findstr :9001  # 检查端口占用
```

### 5. 防火墙阻止
```
问题: UDP数据发送失败
解决: 在Windows防火墙中允许程序通过
```

## 📊 Windows环境监控

### 查看进程
```cmd
# 查看运行中的工具
tasklist | findstr multi_sensor_tool
tasklist | findstr rtsp_tool
```

### 查看网络连接
```cmd
# 查看RTSP连接
netstat -an | findstr :8554

# 查看UDP连接
netstat -an | findstr :9001
```

### 查看资源使用
```cmd
# 使用任务管理器或
wmic process where name="multi_sensor_tool.exe" get ProcessId,PageFileUsage,WorkingSetSize
```

## 🔧 开发环境设置

### Visual Studio Code设置
1. 安装C/C++扩展
2. 配置编译器路径: `C:\msys64\mingw64\bin\g++.exe`
3. 配置包含路径: `C:\msys64\mingw64\include`

### 调试设置
```json
// .vscode/launch.json
{
    "version": "0.2.0",
    "configurations": [
        {
            "name": "Debug Multi-Sensor Tool",
            "type": "cppdbg",
            "request": "launch",
            "program": "${workspaceFolder}/build/bin/multi_sensor_tool.exe",
            "args": ["-f", "test/configs/test_config.txt", "-g", "json-only-test", "-d"],
            "stopAtEntry": false,
            "cwd": "${workspaceFolder}",
            "environment": [],
            "externalConsole": false,
            "MIMode": "gdb",
            "miDebuggerPath": "C:/msys64/mingw64/bin/gdb.exe"
        }
    ]
}
```

## 📝 Windows特定注意事项

1. **路径分隔符**: Windows使用反斜杠 `\`，但代码中已兼容
2. **网络库**: 代码已包含Windows网络库支持 (`ws2_32.lib`)
3. **信号处理**: Windows不支持某些UNIX信号，代码已适配
4. **文件编码**: 确保配置文件使用UTF-8编码

## 🚀 快速验证

### 完整测试流程
```cmd
# 1. 编译工具
scripts\build_multi_sensor.bat

# 2. 生成测试数据
scripts\generate_test_data.bat

# 3. 启动UDP监听器（PowerShell窗口）
powershell -ExecutionPolicy Bypass -File scripts\udp_listener.ps1 -Port 9001

# 4. 运行工具（命令提示符窗口）
build\bin\multi_sensor_tool.exe -f test\configs\test_config.txt -g json-only-test -d
```

如果看到UDP监听器收到JSON数据，说明工具运行正常！
