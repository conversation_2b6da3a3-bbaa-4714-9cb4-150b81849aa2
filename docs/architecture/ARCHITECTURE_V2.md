# 多传感器数据同步播放工具 - 架构设计文档 v2.0

## 1. 项目概述

### 1.1 设计目标
基于现有RTSP工具，扩展为支持多传感器数据同步播放的综合工具，包括：
- 普通视频流 (TS格式)
- 鱼眼视频流 (TS格式) 
- Radar数据 (JSON格式)
- Lidar数据 (JSON格式)

### 1.2 核心原则
- **向后兼容**: 保持现有RTSP工具的所有功能
- **模块化设计**: 传感器类型可扩展
- **统一时序**: 所有传感器数据按时间戳统一调度
- **最小改动**: 基于现有代码结构进行扩展

## 2. 整体架构

### 2.1 分层架构图

```
┌─────────────────────────────────────────────────────────────┐
│                    应用层 (Application Layer)                │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
│  │   命令行接口     │  │   配置文件解析   │  │   错误处理      │ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│                    控制层 (Control Layer)                   │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
│  │  多传感器管理器  │  │   时序调度器     │  │   播放控制器    │ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│                    数据层 (Data Layer)                      │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
│  │   视频处理器     │  │   JSON处理器    │  │   时间戳管理器   │ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│                    输出层 (Output Layer)                    │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
│  │   RTSP推流器     │  │   UDP发送器     │  │   网络管理器    │ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

### 2.2 核心组件关系

```mermaid
graph TB
    A[MultiSensorManager] --> B[TimeScheduler]
    A --> C[VideoSensor]
    A --> D[FisheyeVideoSensor]
    A --> E[RadarSensor]
    A --> F[LidarSensor]
    
    B --> G[TimestampManager]
    
    C --> H[RTSPStreamer]
    D --> H
    E --> I[UDPSender]
    F --> I
    
    J[ConfigParser] --> A
    K[CommandLineInterface] --> A
```

## 3. 核心组件设计

### 3.1 传感器抽象基类

```cpp
class SensorBase {
public:
    enum SensorType { 
        VIDEO,          // 普通视频
        FISHEYE_VIDEO,  // 鱼眼视频
        RADAR,          // Radar数据
        LIDAR           // Lidar数据
    };
    
    enum OutputType {
        RTSP_STREAM,    // RTSP推流
        UDP_PACKET      // UDP数据包
    };
    
    virtual ~SensorBase() = default;
    
    // 核心接口
    virtual bool initialize() = 0;
    virtual bool loadData() = 0;
    virtual bool getNextFrame(int64_t target_timestamp) = 0;
    virtual bool sendFrame() = 0;
    virtual void cleanup() = 0;
    
    // 状态查询
    virtual int64_t getCurrentTimestamp() = 0;
    virtual SensorType getType() = 0;
    virtual OutputType getOutputType() = 0;
    virtual bool isDataAvailable() = 0;
    virtual bool hasMoreData() = 0;
    
    // 配置信息
    virtual std::string getDataFile() = 0;
    virtual std::string getTimestampFile() = 0;
    virtual std::string getOutputAddress() = 0;
    virtual int getOutputPort() = 0;
};
```

### 3.2 具体传感器实现

#### 3.2.1 VideoSensor (视频传感器)
```cpp
class VideoSensor : public SensorBase {
private:
    StreamContext stream_context_;  // 复用现有结构
    // ... 其他成员变量
    
public:
    SensorType getType() override { return VIDEO; }
    OutputType getOutputType() override { return RTSP_STREAM; }
    // 实现基类所有虚函数
};
```

#### 3.2.2 RadarSensor/LidarSensor (JSON数据传感器)
```cpp
class JsonSensorBase : public SensorBase {
protected:
    std::vector<JsonFrame> frames_;
    size_t current_frame_index_;
    std::string udp_address_;
    int udp_port_;
    
    struct JsonFrame {
        int64_t timestamp;
        std::string json_data;
    };
    
public:
    OutputType getOutputType() override { return UDP_PACKET; }
    // JSON解析和UDP发送逻辑
};

class RadarSensor : public JsonSensorBase {
public:
    SensorType getType() override { return RADAR; }
};

class LidarSensor : public JsonSensorBase {
public:
    SensorType getType() override { return LIDAR; }
};
```

### 3.3 时序调度器 (AdaptiveTimeScheduler)

```cpp
class AdaptiveTimeScheduler {
private:
    struct TimeEvent {
        int64_t timestamp;
        SensorBase* sensor;
        size_t sensor_id;

        bool operator<(const TimeEvent& other) const {
            return timestamp > other.timestamp; // 优先队列最小堆
        }
    };

    std::priority_queue<TimeEvent> event_queue_;
    std::vector<SensorBase*> sensors_;
    bool is_loop_mode_;
    int64_t earliest_timestamp_ = INT64_MAX;

public:
    void addSensor(SensorBase* sensor);
    void buildEventQueue();
    void processEvents();  // 主调度循环
    void reset();  // 循环播放重置

private:
    void processReadyEvents(int64_t current_time);
    int64_t calculateAdaptiveSleepTime(int64_t current_time);
    int64_t get_timestamp_ms();
};
```

**高精度触发策略**

AdaptiveTimeScheduler 在事件驱动模型下，通过“长睡眠 → 短睡眠 → 忙等”三级自适应机制保证触发误差 ≤1–2 ms，核心流程如下：

1. 计算 `wait_time = next_event.timestamp - now`  
2. Sleep 决策  
   • wait_time > 20 ms → sleep(wait_time - 2 ms)  
   • 5 ms < wait_time ≤ 20 ms → sleep 2 ms  
   • 1 ms < wait_time ≤ 5 ms → sleep 1 ms（高分辨率）  
   • wait_time ≤ 1 ms → 进入忙等  
3. 忙等窗口内不再调用 sleep，而循环 `std::this_thread::yield()`/`cpu_relax()`，一旦 `now ≥ next_event.timestamp` 立即触发 `sendFrame()`。

示例实现片段：

```cpp
void AdaptiveTimeScheduler::processEvents() {
    while (true) {
        int64_t now = get_timestamp_ms();
        processReadyEvents(now);

        if (event_queue_.empty()) {
            std::this_thread::sleep_for(std::chrono::milliseconds(2));
            continue;
        }

        int64_t next_ts = event_queue_.top().timestamp;
        int64_t wait = next_ts - now;

        if (wait <= 0) continue;

        int64_t sleep_ms = calculateAdaptiveSleepTime(now);
        if (sleep_ms > 0) {
            std::this_thread::sleep_for(std::chrono::milliseconds(sleep_ms));
        } else {
            // busy-wait in the last 1 – 2 ms window for sub-ms accuracy
            while (get_timestamp_ms() < next_ts) {
                std::this_thread::yield(); // 或 _mm_pause() on x86
            }
        }
    }
}
```

> Windows 平台请在程序初始化调用 `timeBeginPeriod(1)`；Linux 可将调度线程置为 `SCHED_FIFO` 并锁内存以降低抖动。

### 3.4 多传感器管理器 (MultiSensorManager)

```cpp
class MultiSensorManager {
private:
    std::vector<std::unique_ptr<SensorBase>> sensors_;
    std::unique_ptr<AdaptiveTimeScheduler> scheduler_;
    bool is_loop_mode_;
    bool is_debug_mode_;
    int64_t program_start_time_;

public:
    bool loadConfiguration(const std::string& config_file, const std::string& group_name);
    bool initializeAllSensors();
    void startPlayback();
    void stopPlayback();

private:
    std::unique_ptr<SensorBase> createSensor(const SensorConfig& config);
    int64_t get_timestamp_ms();
};
```

## 4. 配置文件设计

### 4.1 扩展INI格式

```ini
# 多传感器配置文件 - 扩展INI格式
# 支持视频、鱼眼、Radar、Lidar多种传感器

[scene-001]
# 格式: 传感器类型,数据文件,时间戳文件,输出地址,输出端口
video,v_camera1.ts,v_camera1.txt,rtsp://192.168.1.100:8554/stream1,
fisheye,v_fisheye1.ts,v_fisheye1.txt,rtsp://192.168.1.100:8554/stream2,
radar,radar_data.json,,udp://192.168.1.100,9001
lidar,lidar_data.json,,udp://192.168.1.100,9002

[scene-002]
video,v_camera2.ts,v_camera2.txt,rtsp://192.168.1.100:8554/stream3,
radar,radar_data2.json,,udp://192.168.1.100,9003

# 向后兼容 - 原有格式自动识别为video类型
[legacy-group]
v_10.5.200.218_1723169047302.ts,v_10.5.200.218_1723169047302.txt,rtsp://root:root@192.168.50.233:8554/stream1
v_10.5.200.223_1723169047327.ts,v_10.5.200.223_1723169047327.txt,rtsp://root:root@192.168.50.233:8554/stream2
```

### 4.2 配置字段说明

| 字段位置 | 字段名称 | 说明 | 示例 |
|---------|---------|------|------|
| 1 | 传感器类型 | video/fisheye/radar/lidar | video |
| 2 | 数据文件 | 数据文件路径 | camera1.ts |
| 3 | 时间戳文件 | 时间戳文件路径(JSON类型可为空) | camera1.txt |
| 4 | 输出地址 | RTSP地址或UDP地址 | rtsp://192.168.1.100:8554/stream1 |
| 5 | 输出端口 | UDP端口(RTSP可为空) | 9001 |

## 5. 数据格式规范

### 5.1 时间戳文件格式 (视频类型)
```
1723169047372
1723169047412
1723169047452
...
```

### 5.2 JSON数据文件格式 (Radar/Lidar)
```json
[
    {
        "timestamp": 1723169047372,
        "data": {
            "sensor_id": "radar_001",
            "detection_count": 5,
            "detections": [
                {
                    "range": 25.6,
                    "angle": 15.2,
                    "velocity": 12.3
                }
            ]
        }
    },
    {
        "timestamp": 1723169047412,
        "data": {
            "sensor_id": "radar_001",
            "detection_count": 3,
            "detections": [...]
        }
    }
]
```

### 5.3 UDP输出数据格式
```json
{
    "timestamp": 1723169047372,
    "sensor_type": "radar",
    "sensor_id": "radar_001", 
    "data": { /* 原始传感器数据 */ }
}
```

## 6. 命令行接口

### 6.1 扩展命令行参数
```bash
./multi_sensor_tool -f <配置文件> -g <传感器组名称> [-l] [-d] [-t <类型>] [-h]

选项说明：
  -f <配置文件>  必须指定的INI格式配置文件
  -g <组名称>    必须指定要播放的传感器组名称  
  -d            输出调试信息
  -l            循环播放模式
  -h            显示帮助信息
  -t <类型>     设置视频SEI帧类型 (0: 默认, 1: 大华, 2: 海康)
```

### 6.2 使用示例
```bash
# 播放多传感器场景
./multi_sensor_tool -f multi_sensor_config.txt -g scene-001 -l -d

# 向后兼容 - 播放纯视频场景  
./multi_sensor_tool -f rtspconfig.txt -g deqing-001 -l -d
```

## 7. 实现计划

### 7.1 Phase 1: 基础架构 (1-2周)
- [ ] 设计并实现传感器抽象基类
- [ ] 扩展配置文件解析器支持新格式
- [ ] 实现时序调度器核心逻辑
- [ ] 向后兼容性测试

### 7.2 Phase 2: 传感器实现 (2-3周)
- [ ] 重构现有StreamContext为VideoSensor
- [ ] 实现FisheyeVideoSensor (复用VideoSensor)
- [ ] 实现RadarSensor和LidarSensor
- [ ] 添加UDP输出功能模块
- [ ] 实现自适应精度调度器
- [ ] 实现分层内存管理系统

### 7.3 Phase 3: 集成与优化 (1-2周)
- [ ] 多传感器联合播放测试
- [ ] 时序精确性验证和调优
- [ ] 内存管理策略验证和优化
- [ ] 性能监控和告警系统
- [ ] 错误处理和异常恢复
- [ ] 文档更新和用户手册

## 8. 时序精确性设计与分析

### 8.1 原有方案的时序精确性机制

#### 8.1.1 核心时间控制逻辑
```cpp
while (true) {
    time_now = get_timestamp_ms();  // 每次循环获取当前时间

    for (auto &stream : streams_) {
        // 精确的时间判断：只有到达播放时间才处理
        if (time_interval + stream.time_base[stream.frame_seq] > time_now) {
            continue;  // 还没到播放时间，跳过
        }

        // 立即处理帧
        // ... 读取、处理、发送帧
    }

    // 2ms休眠，然后重新检查
    std::this_thread::sleep_for(std::chrono::milliseconds(2));
}
```

#### 8.1.2 原有方案的精确性特点
- **轮询精度**: 2ms一次检查
- **时间判断**: 每次循环都重新获取系统时间
- **即时响应**: 一旦时间到达，立即处理
- **最大延迟**: 理论上最大2ms延迟
- **时序保证**: 通过轮询确保不错过任何时间点

### 8.2 新方案的时序挑战与解决方案

#### 8.2.1 核心挑战
**问题**: 纯事件驱动方案如何保证在精确时间点触发事件？

**场景示例**:
```
当前时间: 1000ms
下一帧时间: 1005ms (5ms后)
挑战: 如何保证在1005ms这个精确时刻触发事件？
```

#### 8.2.2 解决方案：自适应精度调度器

```cpp
class AdaptiveTimeScheduler {
private:
    std::priority_queue<TimeEvent> event_queue_;

public:
    void processEvents() {
        while (true) {
            int64_t current_time = get_timestamp_ms();

            // 处理所有到期的事件
            processReadyEvents(current_time);

            // 自适应睡眠策略
            int64_t sleep_time = calculateAdaptiveSleepTime(current_time);

            if (sleep_time > 0) {
                std::this_thread::sleep_for(
                    std::chrono::milliseconds(sleep_time)
                );
            }
        }
    }

private:
    void processReadyEvents(int64_t current_time) {
        std::vector<TimeEvent> ready_events;

        // 收集所有到期事件
        while (!event_queue_.empty() &&
               event_queue_.top().timestamp <= current_time) {
            ready_events.push_back(event_queue_.top());
            event_queue_.pop();
        }

        // 按时间戳排序（保证精确的先后关系）
        std::sort(ready_events.begin(), ready_events.end(),
                 [](const TimeEvent& a, const TimeEvent& b) {
                     return a.timestamp < b.timestamp;
                 });

        // 依次处理
        for (const auto& event : ready_events) {
            event.sensor->sendFrame();
        }
    }

    int64_t calculateAdaptiveSleepTime(int64_t current_time) {
        if (event_queue_.empty()) {
            return 2; // 默认间隔，与原方案保持一致
        }

        int64_t next_event_time = event_queue_.top().timestamp;
        int64_t wait_time = next_event_time - current_time;

        if (wait_time <= 0) {
            return 0; // 立即处理
        } else if (wait_time <= 5) {
            return 1; // 高精度模式：1ms
        } else if (wait_time <= 20) {
            return 2; // 标准模式：2ms（与原方案一致）
        } else {
            return std::min(wait_time - 2, 10L); // 预留2ms缓冲
        }
    }
};
```

#### 8.2.3 时序精确性对比分析

| 方案 | 最大延迟 | CPU占用 | 时序精确性 | 先后关系保证 | 复杂度 |
|------|---------|---------|-----------|-------------|--------|
| **原有轮询** | 2ms | 中等 | 高 | 依赖处理顺序 | 低 |
| **纯事件驱动** | 不确定 | 低 | 差 | 优秀 | 中等 |
| **自适应调度** | 1-2ms | 中等 | 很高 | 优秀 | 高 |

#### 8.2.4 新方案的精确性保证

**关键设计要点**:
1. **保持轮询机制**: 确保时间精确性，避免错过时间点
2. **事件队列排序**: 保证严格的时间戳先后关系
3. **自适应间隔**: 根据事件密度调整轮询频率
4. **缓冲机制**: 预留时间缓冲，避免边界条件错误

**精确性公式**:
```
播放条件：current_time >= event.timestamp
处理顺序：按 event.timestamp 严格排序
最大延迟：min(adaptive_sleep_time, 2ms)
```

### 8.3 技术风险与对策

## 9. 内存管理策略设计

### 9.1 数据规模分析

#### 9.1.1 典型数据规模
- **视频文件**: 单个200-800MB
- **Radar/Lidar JSON**: 单个20-80MB (最大100MB)
- **时间戳文件**: 几KB到几MB

#### 9.1.2 内存使用模式
- **视频流**: FFmpeg解码缓冲 + 预加载缓冲
- **JSON数据**: 全量或分段加载到内存
- **时间戳数据**: 全量加载（占用极小）

### 9.2 分层内存管理架构

```cpp
class MemoryManager {
public:
    enum class DataType {
        VIDEO_BUFFER,    // 视频缓冲区
        JSON_DATA,       // JSON数据
        TIMESTAMP_DATA   // 时间戳数据
    };

    enum class Priority {
        HIGH,    // 当前播放窗口数据
        MEDIUM,  // 预加载数据
        LOW      // 历史数据（循环播放用）
    };

private:
    // 内存限制配置
    struct MemoryLimits {
        size_t total_limit = 2 * 1024 * 1024 * 1024;      // 总限制2GB
        size_t video_limit = 1.5 * 1024 * 1024 * 1024;    // 视频1.5GB
        size_t json_limit = 400 * 1024 * 1024;            // JSON 400MB
        size_t timestamp_limit = 100 * 1024 * 1024;       // 时间戳100MB

        // 单传感器限制
        size_t max_video_per_sensor = 200 * 1024 * 1024;  // 单视频200MB缓冲
        size_t max_json_per_sensor = 100 * 1024 * 1024;   // 单JSON 100MB
    };

public:
    bool allocateMemory(DataType type, size_t size, Priority priority);
    void releaseMemory(DataType type, size_t size);
    bool isMemoryAvailable(DataType type, size_t required_size);
    void forceCleanup(DataType type);
};
```

### 9.3 视频内存管理策略

```cpp
class VideoMemoryManager {
private:
    struct VideoBufferConfig {
        size_t decode_buffer_frames = 30;        // 解码缓冲30帧
        size_t preload_seconds = 10;             // 预加载10秒
        size_t max_buffer_size = 200 * 1024 * 1024; // 最大200MB缓冲
    };

    struct VideoBuffer {
        std::queue<AVPacket*> packet_queue;
        size_t current_size = 0;
        int64_t earliest_timestamp = 0;
        int64_t latest_timestamp = 0;
    };

public:
    bool preloadVideoData(VideoSensor* sensor, int64_t start_time, int64_t duration);
    void releaseExpiredFrames(VideoSensor* sensor, int64_t current_time);
    AVPacket* getNextPacket(VideoSensor* sensor, int64_t target_time);
};
```

### 9.4 JSON数据内存管理策略

```cpp
class JsonMemoryManager {
private:
    struct JsonCacheConfig {
        size_t max_cache_size = 100 * 1024 * 1024;  // 最大100MB缓存
        int64_t preload_window_ms = 30000;           // 预加载30秒
        int64_t cleanup_interval_ms = 5000;          // 5秒清理一次
    };

    struct JsonFrameCache {
        struct JsonFrame {
            int64_t timestamp;
            std::string data;
            size_t size;
            Priority priority;
            int64_t last_access_time;
        };

        std::vector<JsonFrame> frames;
        std::unordered_map<int64_t, size_t> timestamp_index;
        size_t current_size = 0;
    };

public:
    bool loadJsonData(JsonSensorBase* sensor, const std::string& file_path);
    const std::string* getJsonFrame(JsonSensorBase* sensor, int64_t timestamp);
    void releaseExpiredFrames(JsonSensorBase* sensor, int64_t current_time);
};
```

### 9.5 智能预加载策略

```cpp
class SmartPreloader {
private:
    struct PreloadConfig {
        int64_t video_preload_window = 10000;    // 视频预加载10秒
        int64_t json_preload_window = 30000;     // JSON预加载30秒
        int64_t preload_trigger_threshold = 5000; // 剩余5秒时触发预加载
    };

public:
    void schedulePreload(SensorBase* sensor, int64_t current_time, int64_t next_time);

private:
    bool shouldTriggerPreload(SensorBase* sensor, int64_t current_time);
    void executePreload(SensorBase* sensor, int64_t start_time, int64_t end_time);
};
```

### 9.6 循环播放内存优化

```cpp
class LoopPlaybackMemoryManager {
private:
    struct LoopConfig {
        bool enable_full_cache = false;          // 是否启用全量缓存
        size_t loop_cache_limit = 500 * 1024 * 1024; // 循环缓存限制500MB
        bool smart_cache_mode = true;            // 智能缓存模式
    };

public:
    bool enableLoopCache(SensorBase* sensor);
    bool getCachedData(SensorBase* sensor, int64_t timestamp, uint8_t** data, size_t* size);
    void addToLoopCache(SensorBase* sensor, int64_t timestamp, const uint8_t* data, size_t size);
};
```

### 9.7 内存监控和告警

```cpp
class MemoryMonitor {
private:
    struct MonitorConfig {
        double warning_threshold = 0.8;    // 80%内存使用率告警
        double critical_threshold = 0.95;  // 95%内存使用率严重告警
        int64_t check_interval_ms = 1000;  // 1秒检查一次
        bool enable_auto_cleanup = true;   // 启用自动清理
    };

public:
    void startMonitoring();
    void checkMemoryUsage();
    void triggerAutoCleanup();
};
```

### 9.8 内存管理策略总结

#### 9.8.1 核心策略
1. **分类管理**: 视频、JSON、时间戳数据分别管理
2. **优先级缓存**: 高/中/低优先级数据分级处理
3. **智能预加载**: 基于播放进度预测性加载
4. **自动清理**: 过期数据及时释放
5. **循环优化**: 循环播放模式的特殊优化

#### 9.8.2 内存分配策略
| 数据类型 | 单传感器限制 | 总体限制 | 管理策略 |
|---------|-------------|---------|---------|
| 视频缓冲 | 200MB | 1.5GB | 滑动窗口+预加载 |
| JSON数据 | 100MB | 400MB | 全量加载+LRU清理 |
| 时间戳 | 无限制 | 100MB | 全量加载 |

#### 9.8.3 性能指标
- **内存使用效率**: > 85%
- **缓存命中率**: > 95%
- **预加载准确率**: > 90%
- **内存泄漏**: 0容忍

## 10. 技术风险与对策

### 10.1 主要风险
| 风险项 | 影响程度 | 对策 |
|-------|---------|------|
| 时序同步精度 | 高 | 采用自适应精度调度器 |
| 内存消耗过大 | 高 | 实现分层内存管理策略 |
| JSON解析性能 | 中 | 使用轻量级JSON库，按需解析 |
| 事件队列性能 | 中 | 优化优先队列实现 |
| UDP传输可靠性 | 低 | 添加重传机制(可选) |
| 向后兼容性 | 高 | 充分测试，保持API稳定 |

### 10.2 性能指标
- **内存使用**: 视频传感器 < 200MB, JSON传感器 < 100MB
- **CPU占用**: 多传感器播放 < 50%
- **时序精度**: ±1-2ms（与原方案相当或更优）
- **网络延迟**: UDP < 5ms, RTSP < 50ms
- **事件处理延迟**: < 1ms
- **内存使用效率**: > 85%
- **缓存命中率**: > 95%

## 9. 测试策略

### 9.1 单元测试
- 各传感器类的独立功能测试
- 配置文件解析器测试
- 时序调度器算法测试

### 9.2 集成测试  
- 多传感器同步播放测试
- 循环播放功能测试
- 网络输出稳定性测试

### 9.3 兼容性测试
- 原有配置文件格式兼容性
- 不同操作系统兼容性
- 不同FFmpeg版本兼容性

---

**文档版本**: v2.0  
**创建日期**: 2025-01-XX  
**最后更新**: 2025-01-XX  
**维护者**: 架构团队
