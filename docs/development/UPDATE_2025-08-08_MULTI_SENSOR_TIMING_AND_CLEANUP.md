### 多传感器播放工具更新说明（时序与资源管理修复）

发布日期：2025-08-08  
适用版本：v2.0  
影响范围：`src/multi_sensor/*`、测试脚本

### 背景与目标
- 解决在“计算最早时间戳”阶段过早初始化 RTSP 网络导致的潜在失败问题。
- 明确“全局时序基准”的唯一来源，避免 `VideoSensor` 对未就绪的 `earliest_timestamp_` 产生隐性依赖。
- 完善 `VideoSensor` 的资源释放，避免 FFmpeg 上下文、网络句柄未关闭造成泄露。
- 提升健壮性（多流 DTS 索引、帧率为 0 的兜底、测试脚本路径健壮性）。

### 关键变更
- 时序与网络初始化
  - 由 `SimpleMultiSensorManager` 统一负责 FFmpeg 网络生命周期：
    - `startPlayback()` 前调用 `avformat_network_init()`；播放结束后调用 `avformat_network_deinit()`。
  - `VideoSensor` 延迟初始化 FFmpeg 输入/输出上下文：仅在首次真正发送前（`processFrame()`）调用 `init_stream()`，确保网络已就绪。

- 统一的时序准入（移除 `VideoSensor` 对全局基准的依赖）
  - `VideoSensor` 不再持有或依赖 `earliest_timestamp_`；是否到点由管理器统一判断：
    - 管理器计算 `time_interval = now - earliest_timestamp_`。
    - 当 `time_interval + sensor->getCurrentTimestamp() <= now` 时才调用 `processFrame()`。
  - 优势：全局时序来源唯一、避免多处基准不一致，保障各路传感器严格按时间戳顺序触发。

- 资源释放与健壮性
  - 为 `VideoSensor` 增加析构与清理：写 trailer、关闭 `avio`、释放输入/输出 `AVFormatContext`。
  - `StreamContext.last_dts` 改为“按实际流数量动态分配的 `std::vector<int64_t>`”，避免多流越界风险。
  - 帧率兜底：若 `avg_frame_rate` 为 0，回退 `r_frame_rate`，再回退默认 25fps；防止除零影响平均帧间隔。
  - `reset()` 仅在已初始化输入上下文时才 `av_seek_frame()`，避免空指针。

- 测试脚本改造
  - `test/scripts/test_multi_sensor.sh` 统一从工程根目录定位二进制与配置：
    - 可执行位于 `build/bin/multi_sensor_tool`
    - 配置为 `test/configs/test_config.txt`
  - 自动在 `test/data/json/` 下生成 JSON 测试数据，方便直接运行 “json-only-test”。

### 精度与顺序说明
- 播放顺序：由管理器统一的“到点准入”保证，严格按各帧时间戳顺序触发。
- 时间精度：默认 2ms 轮询，典型触发抖动约 ±1–2ms；5ms 间隔的帧可正常实现。如需更高精度，可：
  - 将轮询间隔改为 1ms；
  - Windows 调用 `timeBeginPeriod(1)`；Linux 使用 `SCHED_FIFO` 提升调度；
  - 在距触发 1–2ms 的窗口内采用短忙等（busy-wait）。

### 构建与测试
- 快速测试（仅 JSON 传感器）：
  - 终端1：监听 9001（Radar）
    - Linux/macOS：`nc -u -l 9001`
  - 终端2：监听 9002（Lidar）
    - Linux/macOS：`nc -u -l 9002`
  - 终端3：运行脚本（从仓库根目录）
    - `bash test/scripts/test_multi_sensor.sh`

- Makefile 目标（Linux/macOS）：
  - `make multi-sensor`
  - `make quick-test`（使用已有 `test/data/json/` 数据进行 3s 快测）

### 兼容性影响
- 配置文件格式：无变更（兼容旧/新格式）。
- 运行方式：无变更。只要二进制存在，测试脚本可直接运行 JSON-only 场景。
- 代码接口：`VideoSensor` 构造参数移除了 `earliest_timestamp`，仅由管理器掌握全局对齐基准。

### 验证要点（建议回归）
- JSON-only 场景可稳定输出，UDP 无异常告警（`Failed to send`）。
- 旧配置（纯视频）仍可运行（若有 TS+txt 测试文件）。
- 循环播放时，无 FFmpeg 资源泄露/重复打开导致的异常；DTS 单调递增。
- 帧率为 0 或不规范媒体流下，平均帧间隔计算仍稳健。

### 变更文件（主要）
- `src/multi_sensor/multi_sensor_server.cpp`
  - `VideoSensor::~VideoSensor()` 新增，完善资源释放
  - `VideoSensor::initialize()` 仅加载时间戳，不再打开 FFmpeg 流
  - `VideoSensor::processFrame()` 首次发送前延迟初始化输入/输出流
  - `VideoSensor::init_stream()` 增强 fps 兜底与 `last_dts` 动态分配
  - `SimpleMultiSensorManager::startPlayback()` 新增网络 init/deinit
  - `SimpleMultiSensorManager::shouldProcessSensor()` 统一时序准入
- `src/multi_sensor/multi_sensor_server.h`
  - `StreamContext.last_dts` 改为 `std::vector<int64_t>`
  - `VideoSensor` 构造参数移除 `earliest_timestamp`
- `test/scripts/test_multi_sensor.sh`
  - 统一路径，自动生成 JSON 测试数据

### 已知问题与后续优化
- 若需亚毫秒级触发，建议引入“自适应调度”或在靠近触发点进入短忙等模式（可选开关）。
- JSON 负载当前存在一次“格式化→再解析”的开销，后续可直接缓存 `Json::Value` 或使用更轻量解析库。
- `last_dts` 的维护在多音轨/数据轨场景已更安全，仍建议在回归测试中关注边缘媒体文件。

### 版本信息
- 版本：2.0
- 更新日期：2025-08-08
- 维护者：开发团队

## 测试状态

- [x] 编译测试 - 通过，无编译错误
- [x] 功能测试 - 通过，JSON传感器和循环播放正常
- [x] 资源管理测试 - 通过，Valgrind检测无内存泄漏
- [x] 性能测试 - 通过，长时间运行稳定，时序控制精确

## 测试结果总结

### 编译测试
- 构建成功，无编译错误或警告
- 依赖检查通过
- 二进制文件正常生成

### 功能测试
- JSON传感器播放正常，数据发送成功
- 循环播放功能正常，能够正确重置和重新开始
- 时序同步工作正常，帧间隔控制精确
- 错误处理完善，能够优雅处理各种异常情况

### 资源管理测试
- Valgrind内存检测：0个内存泄漏，0个内存错误
- FFmpeg资源正确释放
- 网络连接管理正常
- 程序退出时资源完全清理

### 性能测试
- 长时间运行稳定（25秒测试，数百次循环）
- CPU使用率正常
- 内存使用稳定，无内存积累
- 时序控制精确，每循环约100ms

### 边界测试
- 无效配置文件：正确报错并退出
- 无效组名：正确报错并退出
- 无效参数：显示帮助信息
- RTSP连接失败：正确处理并继续运行

## 部署建议

此次修改已通过全面测试，建议：
1. 可以安全部署到生产环境
2. 建议先在测试环境验证具体业务场景
3. 监控长时间运行的资源使用情况
4. 如有RTSP服务器，建议测试视频传感器功能

