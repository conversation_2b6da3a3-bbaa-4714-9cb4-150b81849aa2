# 视频剪辑工具使用指南

## 概述

`video_clip_tool` 是一个专门用于剪辑厂家私有协议 TS 格式视频的命令行工具。该工具能够根据 SEI 中嵌入的时间戳信息，精确地剪辑指定时间范围的视频片段。

## 功能特点

- **精确时间戳剪辑**：基于视频中 SEI 嵌入的时间戳进行精确剪辑
- **关键帧对齐**：自动向前查找最近的关键帧，确保剪辑后的视频可正常播放
- **多种时间格式支持**：支持毫秒时间戳、ISO 格式、时分秒格式
- **保持原始编码**：不重新编码，保持原始视频质量和格式
- **自动时间戳调整**：自动调整输出视频的时间戳，确保播放连续性

## 编译安装

### Linux/macOS

```bash
# 进入项目根目录
cd rtsp_tools

# 编译工具
make original

# 工具将生成在 build/bin/video_clip_tool
```

### Windows

```batch
# 进入项目根目录
cd rtsp_tools

# 运行编译脚本
scripts\build\build_original.bat

# 工具将生成在 build\bin\video_clip_tool.exe
```

## 使用方法

### 基本语法

```bash
video_clip_tool <输入视频文件> <输出视频文件> <开始时间> <结束时间>
```

### 时间格式

工具支持以下三种时间格式：

1. **毫秒时间戳**（推荐）
   ```bash
   video_clip_tool input.ts output.ts 1723169047372 1723169050000
   ```

2. **ISO 格式**（支持带毫秒和不带毫秒）
   ```bash
   video_clip_tool input.ts output.ts "2024-08-09T10:30:47.372" "2024-08-09T10:30:50.000"
   video_clip_tool input.ts output.ts "2024-08-09T10:04:10" "2024-08-09T10:04:20"
   ```

3. **时分秒格式**（相对时间）
   ```bash
   video_clip_tool input.ts output.ts "00:00:10.500" "00:00:15.800"
   ```

## 使用示例

### 示例 1：剪辑指定时间范围

假设您有一个视频文件 `v_10.5.200.218_1723169047302.ts`，想要剪辑其中 2 秒的片段：

```bash
# 首先提取时间戳信息
rtsp_timestamp_proc_tool v_10.5.200.218_1723169047302.ts

# 查看时间戳文件，确定剪辑范围
cat v_10.5.200.218_1723169047302.txt

# 剪辑从 1723169047372 到 1723169049372 的 2 秒视频
video_clip_tool v_10.5.200.218_1723169047302.ts clip_2sec.ts 1723169047372 1723169049372
```

### 示例 2：批量剪辑

创建批处理脚本进行批量剪辑：

```bash
#!/bin/bash
# batch_clip.sh

# 设置时间范围
START_TIME=1723169047372
END_TIME=1723169049372

# 批量处理所有 .ts 文件
for video in *.ts; do
    output="clipped_${video}"
    echo "剪辑 $video -> $output"
    video_clip_tool "$video" "$output" $START_TIME $END_TIME
done
```

### 示例 3：结合时间戳提取工具使用

```bash
#!/bin/bash
# smart_clip.sh

VIDEO_FILE=$1
CLIP_DURATION=5000  # 剪辑5秒

# 提取时间戳
rtsp_timestamp_proc_tool "$VIDEO_FILE"

# 读取第一个时间戳
TIMESTAMP_FILE="${VIDEO_FILE%.ts}.txt"
FIRST_TS=$(head -n 1 "$TIMESTAMP_FILE")

# 计算结束时间
END_TS=$((FIRST_TS + CLIP_DURATION))

# 执行剪辑
OUTPUT_FILE="clip_${VIDEO_FILE}"
video_clip_tool "$VIDEO_FILE" "$OUTPUT_FILE" $FIRST_TS $END_TS

echo "剪辑完成：$OUTPUT_FILE"
```

## 工作原理

1. **扫描阶段**
   - 扫描整个视频文件，提取所有帧的 SEI 时间戳
   - 建立时间戳索引，包括 DTS、PTS、帧索引和关键帧信息

2. **定位阶段**
   - 根据指定的开始和结束时间，在时间戳索引中查找对应的帧
   - 自动向前查找最近的关键帧作为实际开始点

3. **剪辑阶段**
   - 复制视频流和音频流的编码参数
   - 只复制指定时间范围内的数据包
   - 调整输出视频的时间戳，确保从 0 开始

## 注意事项

1. **时间戳要求**
   - 输入视频必须包含有效的 SEI 时间戳信息
   - 时间戳值必须大于 1000000000000（约 2001 年以后）

2. **关键帧对齐**
   - 工具会自动向前查找最近的关键帧
   - 实际剪辑的开始时间可能略早于指定时间

3. **性能考虑**
   - 首次运行需要扫描整个视频文件建立索引
   - 大文件扫描可能需要一些时间

4. **输出质量**
   - 工具不会重新编码视频，保持原始质量
   - 输出文件格式与输入文件相同

## 故障排除

### 问题 1：找不到时间戳

**错误信息**：
```
扫描完成，共找到 0 个时间戳帧
```

**解决方法**：
- 确认视频文件包含 SEI 时间戳信息
- 使用 `rtsp_timestamp_proc_tool` 验证时间戳是否存在
- 检查 SEI 数据的偏移量设置是否正确

### 问题 2：时间范围无效

**错误信息**：
```
指定的时间范围内没有数据
```

**解决方法**：
- 检查时间范围是否在视频的实际时间戳范围内
- 使用提取的时间戳文件确认有效的时间范围

### 问题 3：ISO 时间格式解析失败

**错误信息**：
```
无法解析时间字符串: 2024-08-09T10:04:10.000
```

**解决方法**：
- 确保使用正确的 ISO 8601 格式：`YYYY-MM-DDTHH:MM:SS[.mmm]`
- 支持带毫秒：`2024-08-09T10:04:10.000`
- 支持不带毫秒：`2024-08-09T10:04:10`
- 时间字符串需要用引号包围：`"2024-08-09T10:04:10.000"`

### 问题 4：输出视频无法播放

**可能原因**：
- 剪辑范围太短，没有包含足够的关键帧

**解决方法**：
- 增加剪辑时长
- 确保剪辑范围包含至少一个完整的 GOP

## 测试脚本

项目提供了测试脚本用于验证工具功能：

### Linux/macOS
```bash
cd scripts/test
chmod +x test_video_clip.sh
./test_video_clip.sh
```

### Windows
```batch
cd scripts\test
test_video_clip.bat
```

## 相关工具

- **rtsp_timestamp_proc_tool**：提取视频中的 SEI 时间戳
- **rtsp_tool**：RTSP 视频流接收和录制工具
- **multi_sensor_tool**：多传感器数据同步工具

## 技术支持

如遇到问题，请提供以下信息：
1. 使用的命令和参数
2. 错误信息截图或文本
3. 视频文件的基本信息（可使用 ffprobe 查看）
4. 时间戳提取结果（前几行）
