# 批量视频剪辑脚本使用指南

## 概述

批量视频剪辑脚本用于自动化处理指定目录下的所有 TS 视频文件，根据指定的时间范围进行剪辑，并自动生成对应的时间戳文件。

## 脚本文件

- **脚本位置**: `scripts/batch_clip_videos.sh`
- **支持平台**: Linux / macOS

## 功能特点

- ✅ **批量处理** - 自动遍历目录下的所有 TS 文件
- ✅ **多种时间格式** - 支持毫秒时间戳、相对时长等格式
- ✅ **自动时间戳生成** - 为剪辑后的视频生成对应的时间戳文件
- ✅ **智能跳过** - 避免重复处理已存在的文件
- ✅ **详细日志** - 提供处理过程的详细信息
- ✅ **统计报告** - 显示处理结果统计

## 使用方法

### 基本语法

```bash
# 基本语法
./scripts/batch_clip_videos.sh <输入目录> <输出目录> <开始时间> <结束时间> [选项]

# 给脚本添加执行权限
chmod +x scripts/batch_clip_videos.sh
```

## 时间格式支持

### 1. 毫秒时间戳（推荐）

适用于有明确起止时间戳的情况：

```bash
./scripts/batch_clip_videos.sh /path/to/videos /path/to/output 1723169047372 1723169050000
```

### 2. 固定时长格式

从视频开始剪辑指定时长：

```bash
# 剪辑每个视频的前30秒
./scripts/batch_clip_videos.sh /path/to/videos /path/to/output 0:30
```

### 3. 相对偏移+时长格式

从指定偏移开始剪辑指定时长：

```bash
# 从第5秒开始，剪辑10秒
./scripts/batch_clip_videos.sh /path/to/videos /path/to/output +5:10

# 从第5秒剪辑到第15秒
./scripts/batch_clip_videos.sh /path/to/videos /path/to/output +5 +15
```

## 选项参数

| 选项 | 说明 | 示例 |
|------|------|------|
| `-h, --help` | 显示帮助信息 | `--help` |
| `-f, --force` | 覆盖已存在的输出文件 | `--force` |
| `-v, --verbose` | 显示详细输出 | `--verbose` |
| `-p, --prefix` | 输出文件前缀 | `--prefix segment_` |
| `-s, --suffix` | 输出文件后缀 | `--suffix _clipped` |
| `--no-timestamp` | 不生成时间戳文件 | `--no-timestamp` |

## 使用示例

### 示例 1: 基本批量剪辑

剪辑目录下所有视频的前30秒：

```bash
./scripts/batch_clip_videos.sh ./test/data/video ./output 0:30 --verbose
```

### 示例 2: 使用时间戳剪辑

基于具体时间戳剪辑：

```bash
./scripts/batch_clip_videos.sh ./test/data/video ./output 1723169047372 1723169050000 --prefix highlight_
```

### 示例 3: 相对时间剪辑

从第10秒开始剪辑5秒：

```bash
./scripts/batch_clip_videos.sh ./test/data/video ./output +10:5 --suffix _segment --verbose
```

### 示例 4: 强制覆盖模式

覆盖已存在的输出文件：

```bash
./scripts/batch_clip_videos.sh ./test/data/video ./output 0:30 --force --no-timestamp
```

## 输出文件命名规则

脚本会根据以下规则生成输出文件名：

```
输出文件名 = 前缀 + 原文件名(不含扩展名) + 后缀 + .ts
时间戳文件名 = 前缀 + 原文件名(不含扩展名) + 后缀 + .txt
```

### 示例

- 原文件: `camera_20240809_103047.ts`
- 前缀: `clip_` (默认)
- 后缀: `_2sec`
- 输出视频: `clip_camera_20240809_103047_2sec.ts`
- 时间戳文件: `clip_camera_20240809_103047_2sec.txt`

## 工作流程

1. **扫描输入目录** - 递归查找所有 `.ts` 文件
2. **检查输出文件** - 如果不是强制模式，跳过已存在的文件
3. **提取时间戳** - 对于相对时间格式，先提取原视频的时间戳
4. **计算剪辑时间** - 根据时间格式计算实际的开始和结束时间戳
5. **执行剪辑** - 调用 `video_clip_tool` 进行剪辑
6. **生成时间戳** - 为剪辑后的视频生成时间戳文件
7. **统计报告** - 显示处理结果统计

## 错误处理

脚本包含完善的错误处理机制：

- **工具检查** - 确认剪辑工具和时间戳工具存在
- **目录验证** - 验证输入目录存在，创建输出目录
- **文件跳过** - 智能跳过无法处理的文件
- **时间戳验证** - 验证时间戳文件的有效性
- **详细日志** - 记录每个步骤的执行结果

## 性能优化建议

1. **使用SSD存储** - 提高文件读写性能
2. **批量处理** - 一次处理多个文件比单个处理效率更高
3. **合理的时间范围** - 避免剪辑过长的片段
4. **预先提取时间戳** - 对于相对时间格式，可以预先提取时间戳文件

## 故障排除

### 问题 1: 工具未找到

**错误信息**：
```
[ERROR] 视频剪辑工具未找到
```

**解决方法**：
```bash
# 编译项目
make original

# 检查工具是否存在
ls -la build/bin/
```

### 问题 2: 时间戳提取失败

**错误信息**：
```
[ERROR] 时间戳提取失败
```

**解决方法**：
- 确认视频文件包含有效的 SEI 时间戳
- 手动测试时间戳提取：
  ```bash
  ./build/bin/rtsp_timestamp_proc_tool test_video.ts
  ```

### 问题 3: 权限问题

**错误信息**：
```
Permission denied
```

**解决方法**：
```bash
chmod +x scripts/batch_clip_videos.sh
```

### 问题 4: 输出目录权限不足

**解决方法**：
```bash
# 检查输出目录权限
ls -la /path/to/output/

# 修改权限
chmod 755 /path/to/output/
```

## 高级用法

### 结合其他工具使用

```bash
# 先批量提取时间戳，再批量剪辑
find ./videos -name "*.ts" -exec ./build/bin/rtsp_timestamp_proc_tool {} \;
./scripts/batch_clip_videos.sh ./videos ./output +5:10

# 剪辑后验证结果
for file in ./output/*.ts; do
    echo "检查文件: $file"
    ffprobe "$file" -v quiet -show_entries format=duration -of csv="p=0"
done
```

### 自定义脚本集成

```bash
#!/bin/bash
# 自定义批量处理脚本

INPUT_DIR="/data/videos"
OUTPUT_DIR="/data/clips"
LOG_FILE="/var/log/batch_clip.log"

# 创建日志
echo "$(date): 开始批量剪辑" >> "$LOG_FILE"

# 执行剪辑
./scripts/batch_clip_videos.sh "$INPUT_DIR" "$OUTPUT_DIR" 0:30 --verbose >> "$LOG_FILE" 2>&1

# 后处理
echo "$(date): 批量剪辑完成" >> "$LOG_FILE"

# 发送通知
echo "批量剪辑完成，共处理 $(ls $OUTPUT_DIR/*.ts | wc -l) 个文件" | mail -s "剪辑任务完成" <EMAIL>
```

## 最佳实践

1. **测试运行** - 先用少量文件测试参数是否正确
2. **备份原文件** - 重要文件处理前先备份
3. **监控磁盘空间** - 确保输出目录有足够空间
4. **使用日志** - 开启详细模式记录处理过程
5. **验证结果** - 处理完成后抽样验证剪辑结果

## 技术支持

如遇到问题，请提供：
1. 使用的完整命令
2. 错误信息截图或文本
3. 输入文件的基本信息
4. 系统环境信息（操作系统、工具版本等）
