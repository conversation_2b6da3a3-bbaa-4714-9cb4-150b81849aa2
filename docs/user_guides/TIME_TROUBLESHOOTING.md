# 时间格式故障排除指南

## 问题：开始时间必须小于结束时间

当使用 ISO 时间格式时出现此错误，通常是因为时间解析或时区问题。

### 快速解决方案

#### 方案 1：使用毫秒时间戳（推荐）

```bash
# 首先提取视频的实际时间戳
./build/bin/rtsp_timestamp_proc_tool clip_v_10.5.200.238_1723169047332.ts

# 查看时间戳文件内容
head -5 clip_v_10.5.200.238_1723169047332.txt
tail -5 clip_v_10.5.200.238_1723169047332.txt

# 使用实际的时间戳进行剪辑（例如剪辑前10秒）
# 假设第一个时间戳是 1723169047372，剪辑10秒
./build/bin/video_clip_tool clip_v_10.5.200.238_1723169047332.ts output.ts 1723169047372 1723169057372
```

#### 方案 2：计算正确的 ISO 时间

根据文件名中的时间戳 `1723169047332`，对应的实际时间大约是：

```bash
# 时间戳转换（需要考虑时区）
# 1723169047332 毫秒 ≈ 2024-08-09 02:04:07.332 UTC
# 如果是中国时区 (UTC+8)，则是 2024-08-09 10:04:07.332

# 正确的 ISO 时间应该是：
./build/bin/video_clip_tool clip_v_10.5.200.238_1723169047332.ts output.ts \
  "2024-08-09T02:04:07.372" "2024-08-09T02:04:17.372"

# 或者如果考虑中国时区：
./build/bin/video_clip_tool clip_v_10.5.200.238_1723169047332.ts output.ts \
  "2024-08-09T10:04:07.372" "2024-08-09T10:04:17.372"
```

### 详细分析步骤

#### 1. 检查视频实际时间戳

```bash
# 提取时间戳
./build/bin/rtsp_timestamp_proc_tool your_video.ts

# 查看时间戳范围
echo "第一个时间戳:"
head -n 1 your_video.txt

echo "最后一个时间戳:"
tail -n 1 your_video.txt

echo "总帧数:"
wc -l your_video.txt
```

#### 2. 时间戳转换工具

创建一个简单的转换脚本：

```bash
#!/bin/bash
# timestamp_converter.sh

if [ $# -ne 1 ]; then
    echo "用法: $0 <毫秒时间戳>"
    echo "示例: $0 1723169047372"
    exit 1
fi

timestamp_ms=$1
timestamp_s=$((timestamp_ms / 1000))

echo "毫秒时间戳: $timestamp_ms"
echo "秒时间戳: $timestamp_s"
echo "UTC时间: $(date -u -d @$timestamp_s '+%Y-%m-%dT%H:%M:%S').$(printf "%03d" $((timestamp_ms % 1000)))"
echo "本地时间: $(date -d @$timestamp_s '+%Y-%m-%dT%H:%M:%S').$(printf "%03d" $((timestamp_ms % 1000)))"
```

#### 3. 使用相对时间（推荐用于测试）

```bash
# 使用相对时间格式，从视频开始剪辑
./build/bin/video_clip_tool your_video.ts output.ts "00:00:00.000" "00:00:10.000"

# 或者从第5秒开始剪辑5秒
./build/bin/video_clip_tool your_video.ts output.ts "00:00:05.000" "00:00:10.000"
```

### 常见时区问题

#### 中国时区 (UTC+8)
```bash
# 如果视频是在中国录制的，时间戳可能是 UTC+8
# 需要减去8小时来获得 UTC 时间，或者直接使用本地时间

# 方法1：转换为 UTC 时间
./build/bin/video_clip_tool video.ts output.ts \
  "2024-08-09T02:04:07.000" "2024-08-09T02:04:17.000"

# 方法2：使用本地时间（如果系统时区设置正确）
./build/bin/video_clip_tool video.ts output.ts \
  "2024-08-09T10:04:07.000" "2024-08-09T10:04:17.000"
```

### 调试技巧

#### 1. 验证时间解析

创建一个简单的测试：

```bash
# 测试时间解析是否正确
echo "测试时间解析:"
./build/bin/video_clip_tool --help

# 或者创建一个很短的测试剪辑
./build/bin/video_clip_tool video.ts test_output.ts \
  "$(head -n 1 video.txt)" "$(head -n 2 video.txt | tail -n 1)"
```

#### 2. 使用批量脚本

```bash
# 使用批量脚本，自动处理时间计算
./scripts/batch_clip_videos.sh /path/to/video /path/to/output 0:10  # 剪辑前10秒
```

### 最佳实践

1. **优先使用毫秒时间戳**：最准确，避免时区问题
2. **先提取时间戳**：了解视频的实际时间范围
3. **小范围测试**：先剪辑几秒钟验证参数正确
4. **使用批量脚本**：对于大量文件，使用自动化脚本

### 示例工作流

```bash
# 完整的工作流示例
VIDEO_FILE="clip_v_10.5.200.238_1723169047332.ts"

# 1. 提取时间戳
echo "步骤1: 提取时间戳"
./build/bin/rtsp_timestamp_proc_tool "$VIDEO_FILE"

# 2. 查看时间戳范围
echo "步骤2: 查看时间戳范围"
FIRST_TS=$(head -n 1 "${VIDEO_FILE%.ts}.txt")
LAST_TS=$(tail -n 1 "${VIDEO_FILE%.ts}.txt")
echo "时间戳范围: $FIRST_TS - $LAST_TS"

# 3. 计算剪辑范围（剪辑前10秒）
START_TS=$FIRST_TS
END_TS=$((FIRST_TS + 10000))  # 加10秒

echo "步骤3: 剪辑视频"
echo "剪辑范围: $START_TS - $END_TS"

# 4. 执行剪辑
./build/bin/video_clip_tool "$VIDEO_FILE" "output_10sec.ts" "$START_TS" "$END_TS"

echo "剪辑完成: output_10sec.ts"
```

这样可以确保使用正确的时间范围进行剪辑。
