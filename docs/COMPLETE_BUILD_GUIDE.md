# RTSP工具完整编译和使用指南

本文档提供了RTSP工具的详细编译、打包和运行指南，包括独立运行程序的创建方法。

## 目录
- [系统要求](#系统要求)
- [依赖安装](#依赖安装)
- [编译步骤](#编译步骤)
- [独立程序打包](#独立程序打包)
- [使用指南](#使用指南)
- [配置文件格式](#配置文件格式)
- [故障排除](#故障排除)
- [性能优化](#性能优化)
- [部署指南](#部署指南)

## 系统要求

### 支持的操作系统
- **Linux**: Ubuntu 18.04+, CentOS 7+, RHEL 7+, Debian 9+
- **macOS**: 10.15+ (Catalina及以上)
- **Windows**: 10+ (通过WSL2或MinGW-w64)

### 硬件要求
- **CPU**: x86_64架构，支持SSE4.2指令集
- **内存**: 最小512MB，推荐2GB+
- **存储**: 100MB可用空间（不含数据文件）
- **网络**: 千兆以太网（多路高清视频流）

### 依赖库版本要求
- **FFmpeg**: 4.0+ (推荐4.4+)
- **JsonCpp**: 1.8+ (推荐1.9+)
- **GCC**: 4.8+ (支持C++11)
- **CMake**: 3.10+ (可选，用于高级构建)

## 依赖安装

### Ubuntu/Debian系统

```bash
# 更新包管理器
sudo apt-get update

# 安装编译工具链
sudo apt-get install -y \
    build-essential \
    pkg-config \
    cmake \
    git

# 安装FFmpeg开发库
sudo apt-get install -y \
    libavformat-dev \
    libavcodec-dev \
    libavutil-dev \
    libavfilter-dev \
    libswscale-dev \
    libswresample-dev

# 安装JsonCpp库
sudo apt-get install -y \
    libjsoncpp-dev

# 安装其他依赖
sudo apt-get install -y \
    libpthread-stubs0-dev \
    zlib1g-dev
```

### CentOS/RHEL系统

```bash
# 安装开发工具组
sudo yum groupinstall -y "Development Tools"
sudo yum install -y pkg-config cmake3

# 启用EPEL和RPM Fusion仓库
sudo yum install -y epel-release
sudo yum localinstall -y --nogpgcheck \
    https://download1.rpmfusion.org/free/el/rpmfusion-free-release-$(rpm -E %rhel).noarch.rpm

# 安装FFmpeg开发库
sudo yum install -y \
    ffmpeg-devel \
    jsoncpp-devel
```

### macOS系统

```bash
# 安装Xcode命令行工具
xcode-select --install

# 使用Homebrew安装依赖
brew install ffmpeg jsoncpp pkg-config cmake

# 或使用MacPorts（二选一）
sudo port install ffmpeg jsoncpp pkgconfig cmake
```

## 编译步骤

### 标准编译

```bash
# 克隆或进入项目目录
cd rtsp_tools

# 检查依赖
make check-deps

# 清理之前的编译产物
make clean

# 编译所有工具
make all

# 或者单独编译特定工具
make rtsp-tool        # 编译原始RTSP工具
make multi-sensor     # 编译多传感器工具
make timestamp-proc   # 编译时间戳处理工具
```

### 调试版本编译

```bash
# 编译调试版本（包含调试符号）
make DEBUG=1 all

# 编译时启用更多警告
make EXTRA_WARNINGS=1 all
```

### 优化版本编译

```bash
# 编译优化版本（-O3优化）
make RELEASE=1 all

# 编译时启用链接时优化
make LTO=1 all
```

## 独立程序打包

### 静态链接版本

创建不依赖系统库的独立可执行文件：

```bash
# 编译静态链接版本
make STATIC=1 all

# 验证静态链接
ldd build/bin/multi_sensor_tool
# 输出应该显示 "not a dynamic executable" 或只有系统核心库
```

### 创建便携式包

```bash
# 创建便携式发布包
make package

# 这将创建以下结构的tar.gz包：
# rtsp_tools_portable_v1.0_linux_x64.tar.gz
# ├── bin/
# │   ├── multi_sensor_tool
# │   ├── rtsp_tool
# │   └── rtsp_timestamp_proc_tool
# ├── lib/          # 必要的动态库（如果需要）
# ├── configs/      # 示例配置文件
# ├── test_data/    # 测试数据
# ├── docs/         # 文档
# └── run.sh        # 启动脚本
```

### Docker容器化

创建Docker镜像以实现完全独立运行：

```dockerfile
# Dockerfile
FROM ubuntu:22.04

# 安装运行时依赖
RUN apt-get update && apt-get install -y \
    libavformat58 \
    libavcodec58 \
    libavutil56 \
    libjsoncpp25 \
    && rm -rf /var/lib/apt/lists/*

# 复制编译好的程序
COPY build/bin/ /usr/local/bin/
COPY configs/ /etc/rtsp_tools/
COPY test/ /opt/rtsp_tools/test/

# 设置工作目录
WORKDIR /opt/rtsp_tools

# 暴露端口
EXPOSE 8554 9001 9002

# 启动命令
CMD ["multi_sensor_tool", "-f", "/etc/rtsp_tools/config.txt", "-g", "default"]
```

构建Docker镜像：

```bash
# 构建镜像
docker build -t rtsp_tools:latest .

# 运行容器
docker run -d \
    --name rtsp_tools \
    -p 8554:8554 \
    -p 9001:9001/udp \
    -p 9002:9002/udp \
    -v /path/to/data:/data \
    -v /path/to/config:/etc/rtsp_tools \
    rtsp_tools:latest
```

## 使用指南

### 编译输出

编译成功后，在`build/bin/`目录下会生成以下可执行文件：

| 程序名称 | 功能描述 | 主要用途 |
|---------|---------|---------|
| `rtsp_tool` | 原始RTSP多路视频同步播放工具 | 兼容性播放，传统视频流 |
| `multi_sensor_tool` | 多传感器数据同步播放工具 | 多种传感器数据融合播放 |
| `rtsp_timestamp_proc_tool` | 视频时间戳处理工具 | 从视频文件提取时间戳信息 |

### 基本使用方法

#### 1. RTSP工具使用

```bash
# 基本用法
./build/bin/rtsp_tool -f config.txt -g group_name

# 完整参数示例
./build/bin/rtsp_tool \
    -f /path/to/config.txt \    # 配置文件路径
    -g production_group \       # 配置组名称
    -d \                        # 启用调试模式
    -l \                        # 启用循环播放
    -c 1                        # 摄像头类型（0=普通，1=鱼眼）

# 查看帮助信息
./build/bin/rtsp_tool -h
```

#### 2. 多传感器工具使用

```bash
# 基本用法
./build/bin/multi_sensor_tool -f config.txt -g group_name

# 高级用法示例
./build/bin/multi_sensor_tool \
    -f /etc/rtsp_tools/sensors.conf \
    -g vehicle_sensors \
    -d \                        # 调试模式，显示详细日志
    -l \                        # 循环播放模式
    -c 0                        # 摄像头类型

# 后台运行
nohup ./build/bin/multi_sensor_tool \
    -f config.txt -g sensors -l \
    > /var/log/multi_sensor.log 2>&1 &
```

#### 3. 时间戳处理工具使用

```bash
# 从视频文件提取时间戳
./build/bin/rtsp_timestamp_proc_tool input_video.ts > timestamps.txt

# 批量处理多个视频文件
for video in *.ts; do
    echo "Processing $video..."
    ./build/bin/rtsp_timestamp_proc_tool "$video" > "${video%.ts}.txt"
done

# 验证时间戳文件格式
head -10 timestamps.txt
```

### 命令行参数详解

| 参数 | 长格式 | 必需 | 描述 | 示例值 |
|------|--------|------|------|--------|
| `-f` | `--file` | ✅ | 配置文件路径 | `config.txt` |
| `-g` | `--group` | ✅ | 配置文件中的组名 | `sensors_group` |
| `-d` | `--debug` | ❌ | 启用调试模式 | - |
| `-l` | `--loop` | ❌ | 启用循环播放 | - |
| `-c` | `--camera` | ❌ | 摄像头类型 | `0`(普通) `1`(鱼眼) |
| `-h` | `--help` | ❌ | 显示帮助信息 | - |

### 环境变量配置

```bash
# 设置日志级别
export RTSP_LOG_LEVEL=DEBUG    # DEBUG, INFO, WARN, ERROR

# 设置网络超时时间（秒）
export RTSP_TIMEOUT=30

# 设置最大重试次数
export RTSP_MAX_RETRIES=3

# 设置缓冲区大小（字节）
export RTSP_BUFFER_SIZE=1048576

# 使用环境变量运行
./build/bin/multi_sensor_tool -f config.txt -g sensors
```

## 配置文件格式

### 传统RTSP配置格式（向后兼容）

```ini
# rtspconfig.txt
[group_name]
video1.ts,video1.txt,rtsp://ip:port/stream1
video2.ts,video2.txt,rtsp://ip:port/stream2

[deqing-001]
test/data/video/v_10.5.200.218_1723169047302.ts,test/data/video/v_10.5.200.218_1723169047302.txt,rtsp://root:root@**************:8554/stream1
test/data/video/v_10.5.200.223_1723169047327.ts,test/data/video/v_10.5.200.223_1723169047327.txt,rtsp://root:root@**************:8554/stream2
```

### 多传感器配置格式（新格式）

```ini
# multi_sensor_config.txt
[sensor_group]
type=video,data=video1.ts,timestamp=video1.txt,output=rtsp://*************:8554/stream1
type=fisheye,data=fisheye1.ts,timestamp=fisheye1.txt,output=rtsp://*************:8554/fisheye1
type=radar,data=radar_data.json,output=udp://127.0.0.1:9001
type=lidar,data=lidar_data.json,output=udp://127.0.0.1:9002

[json-only-test]
type=radar,data=test/data/json/radar_test.json,output=udp://127.0.0.1:9001
type=lidar,data=test/data/json/lidar_test.json,output=udp://127.0.0.1:9002
```

### 支持的传感器类型

| 传感器类型 | 数据格式 | 输出方式 | 配置示例 |
|-----------|---------|---------|---------|
| `video` | TS + 时间戳文件 | RTSP推流 | `type=video,data=video.ts,timestamp=video.txt,output=rtsp://ip:port/stream` |
| `fisheye` | TS + 时间戳文件 | RTSP推流 | `type=fisheye,data=fisheye.ts,timestamp=fisheye.txt,output=rtsp://ip:port/stream` |
| `radar` | JSON文件 | UDP数据包 | `type=radar,data=radar.json,output=udp://ip:port` |
| `lidar` | JSON文件 | UDP数据包 | `type=lidar,data=lidar.json,output=udp://ip:port` |

### JSON数据格式示例

#### 雷达数据格式
```json
[
  {
    "timestamp": 1723169047302,
    "objects": [
      {
        "id": 1,
        "x": 10.5,
        "y": 20.3,
        "velocity": 15.2,
        "type": "vehicle"
      }
    ]
  }
]
```

#### 激光雷达数据格式
```json
[
  {
    "timestamp": 1723169047302,
    "points": [
      {"x": 1.2, "y": 3.4, "z": 0.5, "intensity": 128},
      {"x": 2.1, "y": 4.3, "z": 0.8, "intensity": 156}
    ]
  }
]
```

## 故障排除

### 常见编译错误

#### 1. 找不到FFmpeg库
```
错误: Package 'libavformat' not found
解决方案:
sudo apt-get install libavformat-dev libavcodec-dev libavutil-dev
```

#### 2. 找不到JsonCpp库
```
错误: Package 'jsoncpp' not found
解决方案:
sudo apt-get install libjsoncpp-dev
```

#### 3. 编译器版本过低
```
错误: C++11 features not supported
解决方案:
# Ubuntu/Debian
sudo apt-get install gcc-4.8 g++-4.8
sudo update-alternatives --install /usr/bin/gcc gcc /usr/bin/gcc-4.8 60
sudo update-alternatives --install /usr/bin/g++ g++ /usr/bin/g++-4.8 60
```

#### 4. pkg-config找不到库
```
错误: Package 'jsoncpp' was not found in the pkg-config search path
解决方案:
export PKG_CONFIG_PATH=/usr/local/lib/pkgconfig:$PKG_CONFIG_PATH
# 或者手动指定库路径
make JSONCPP_CFLAGS="-I/usr/include/jsoncpp" JSONCPP_LIBS="-ljsoncpp"
```

### 常见运行错误

#### 1. 配置文件不存在
```
错误: Cannot open config file: config.txt
解决方案:
- 检查配置文件路径是否正确
- 确保文件存在且有读取权限
- 使用绝对路径：./build/bin/multi_sensor_tool -f /full/path/to/config.txt -g group
```

#### 2. 视频文件不存在
```
错误: Cannot open video file: video.ts
解决方案:
- 检查视频文件路径和权限
- 确保时间戳文件也存在
- 验证文件格式是否正确
```

#### 3. 网络连接失败
```
错误: RTSP connection failed
解决方案:
- 检查网络连接和RTSP服务器状态
- 验证IP地址和端口是否正确
- 检查防火墙设置
- 使用telnet测试连接：telnet ************* 8554
```

#### 4. UDP发送失败
```
错误: UDP socket creation failed
解决方案:
- 检查UDP端口是否被占用：netstat -ulnp | grep 9001
- 验证目标地址是否可达
- 检查防火墙UDP端口设置
```

#### 5. 时间戳文件格式错误
```
错误: Invalid timestamp format
解决方案:
- 确保时间戳文件格式正确（每行一个时间戳）
- 使用rtsp_timestamp_proc_tool重新生成时间戳文件
- 检查时间戳文件编码（应为UTF-8）
```

### 调试技巧

#### 1. 启用详细日志
```bash
# 启用调试模式
./build/bin/multi_sensor_tool -f config.txt -g sensors -d

# 重定向日志到文件
./build/bin/multi_sensor_tool -f config.txt -g sensors -d > debug.log 2>&1
```

#### 2. 网络调试
```bash
# 监听UDP端口
nc -u -l 9001

# 监听RTSP端口
nc -l 8554

# 使用tcpdump抓包
sudo tcpdump -i any -w capture.pcap port 8554 or port 9001
```

#### 3. 内存调试
```bash
# 使用Valgrind检测内存问题
valgrind --leak-check=full --show-leak-kinds=all \
    ./build/bin/multi_sensor_tool -f config.txt -g sensors

# 使用GDB调试
gdb ./build/bin/multi_sensor_tool
(gdb) run -f config.txt -g sensors -d
```

#### 4. 性能分析
```bash
# 使用perf分析性能
perf record ./build/bin/multi_sensor_tool -f config.txt -g sensors
perf report

# 监控系统资源
top -p $(pgrep multi_sensor_tool)
```

## 性能优化

### 系统调优建议

#### 1. 网络优化
```bash
# 增加网络缓冲区大小
echo 'net.core.rmem_max = 134217728' | sudo tee -a /etc/sysctl.conf
echo 'net.core.wmem_max = 134217728' | sudo tee -a /etc/sysctl.conf
echo 'net.core.netdev_max_backlog = 5000' | sudo tee -a /etc/sysctl.conf
sudo sysctl -p

# 优化UDP缓冲区
echo 'net.core.rmem_default = 262144' | sudo tee -a /etc/sysctl.conf
echo 'net.core.wmem_default = 262144' | sudo tee -a /etc/sysctl.conf
```

#### 2. 进程优化
```bash
# 提高进程优先级
nice -n -10 ./build/bin/multi_sensor_tool -f config.txt -g sensors

# 设置CPU亲和性
taskset -c 0,1 ./build/bin/multi_sensor_tool -f config.txt -g sensors

# 设置实时调度
sudo chrt -f 50 ./build/bin/multi_sensor_tool -f config.txt -g sensors
```

#### 3. 存储优化
```bash
# 使用SSD存储数据文件
# 启用文件系统缓存
echo 'vm.dirty_ratio = 15' | sudo tee -a /etc/sysctl.conf
echo 'vm.dirty_background_ratio = 5' | sudo tee -a /etc/sysctl.conf
```

### 编译优化选项

```bash
# 启用所有优化
make RELEASE=1 LTO=1 NATIVE=1 all

# 自定义优化标志
make CXXFLAGS="-O3 -march=native -mtune=native -flto" all
```

## 部署指南

### 生产环境部署

#### 1. 创建专用用户和目录
```bash
# 创建专用用户
sudo useradd -r -s /bin/false -d /opt/rtsp_tools rtsp_user

# 创建目录结构
sudo mkdir -p /opt/rtsp_tools/{bin,configs,data,logs}
sudo mkdir -p /var/log/rtsp_tools

# 复制程序文件
sudo cp build/bin/* /opt/rtsp_tools/bin/
sudo cp -r configs/* /opt/rtsp_tools/configs/
sudo cp -r test/data/* /opt/rtsp_tools/data/

# 设置权限
sudo chown -R rtsp_user:rtsp_user /opt/rtsp_tools
sudo chown -R rtsp_user:rtsp_user /var/log/rtsp_tools
sudo chmod +x /opt/rtsp_tools/bin/*
```

#### 2. 创建systemd服务
```bash
# 创建服务文件
sudo tee /etc/systemd/system/multi-sensor-rtsp.service << 'EOF'
[Unit]
Description=Multi-Sensor RTSP Tool
After=network.target
Wants=network.target

[Service]
Type=simple
User=rtsp_user
Group=rtsp_user
WorkingDirectory=/opt/rtsp_tools
ExecStart=/opt/rtsp_tools/bin/multi_sensor_tool -f /opt/rtsp_tools/configs/production.conf -g production -l
ExecReload=/bin/kill -HUP $MAINPID
Restart=always
RestartSec=5
StandardOutput=journal
StandardError=journal

# 安全设置
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=/opt/rtsp_tools /var/log/rtsp_tools

# 资源限制
LimitNOFILE=65536
LimitNPROC=4096

[Install]
WantedBy=multi-user.target
EOF

# 启用并启动服务
sudo systemctl daemon-reload
sudo systemctl enable multi-sensor-rtsp.service
sudo systemctl start multi-sensor-rtsp.service

# 检查服务状态
sudo systemctl status multi-sensor-rtsp.service
```

#### 3. 配置日志轮转
```bash
# 创建logrotate配置
sudo tee /etc/logrotate.d/rtsp-tools << 'EOF'
/var/log/rtsp_tools/*.log {
    daily
    rotate 30
    compress
    delaycompress
    missingok
    notifempty
    create 644 rtsp_user rtsp_user
    postrotate
        systemctl reload multi-sensor-rtsp.service > /dev/null 2>&1 || true
    endscript
}
EOF
```

#### 4. 防火墙配置
```bash
# Ubuntu/Debian (ufw)
sudo ufw allow 8554/tcp comment 'RTSP'
sudo ufw allow 9001:9010/udp comment 'Sensor UDP'

# CentOS/RHEL (firewalld)
sudo firewall-cmd --permanent --add-port=8554/tcp
sudo firewall-cmd --permanent --add-port=9001-9010/udp
sudo firewall-cmd --reload

# 或者创建自定义服务
sudo tee /etc/firewalld/services/rtsp-tools.xml << 'EOF'
<?xml version="1.0" encoding="utf-8"?>
<service>
  <short>RTSP Tools</short>
  <description>Multi-sensor RTSP streaming tool</description>
  <port protocol="tcp" port="8554"/>
  <port protocol="udp" port="9001-9010"/>
</service>
EOF

sudo firewall-cmd --permanent --add-service=rtsp-tools
sudo firewall-cmd --reload
```

### 监控和维护

#### 1. 健康检查脚本
```bash
# 创建健康检查脚本
sudo tee /opt/rtsp_tools/bin/health_check.sh << 'EOF'
#!/bin/bash

SERVICE_NAME="multi-sensor-rtsp"
LOG_FILE="/var/log/rtsp_tools/health_check.log"
RTSP_PORT=8554
UDP_PORTS=(9001 9002)

log_message() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') - $1" >> "$LOG_FILE"
}

# 检查服务状态
if ! systemctl is-active --quiet "$SERVICE_NAME"; then
    log_message "ERROR: Service $SERVICE_NAME is not running"
    systemctl restart "$SERVICE_NAME"
    exit 1
fi

# 检查RTSP端口
if ! netstat -tlnp | grep -q ":$RTSP_PORT "; then
    log_message "WARNING: RTSP port $RTSP_PORT is not listening"
fi

# 检查UDP端口
for port in "${UDP_PORTS[@]}"; do
    if ! netstat -ulnp | grep -q ":$port "; then
        log_message "INFO: UDP port $port is not in use (normal if no clients)"
    fi
done

# 检查内存使用
MEMORY_USAGE=$(ps -o pid,ppid,cmd,%mem --sort=-%mem -C multi_sensor_tool | tail -n +2 | awk '{print $4}')
if [[ -n "$MEMORY_USAGE" ]] && (( $(echo "$MEMORY_USAGE > 10.0" | bc -l) )); then
    log_message "WARNING: High memory usage: ${MEMORY_USAGE}%"
fi

log_message "INFO: Health check completed successfully"
EOF

sudo chmod +x /opt/rtsp_tools/bin/health_check.sh
sudo chown rtsp_user:rtsp_user /opt/rtsp_tools/bin/health_check.sh
```

#### 2. 定时任务配置
```bash
# 添加crontab任务
sudo crontab -u rtsp_user -e

# 添加以下内容：
# 每5分钟执行健康检查
*/5 * * * * /opt/rtsp_tools/bin/health_check.sh

# 每天凌晨清理旧日志
0 2 * * * find /var/log/rtsp_tools -name "*.log" -mtime +7 -delete

# 每周重启服务（可选）
0 3 * * 0 systemctl restart multi-sensor-rtsp.service
```

#### 3. 监控脚本
```bash
# 创建监控脚本
sudo tee /opt/rtsp_tools/bin/monitor.sh << 'EOF'
#!/bin/bash

INFLUXDB_URL="http://localhost:8086"
DATABASE="rtsp_tools"

# 获取进程信息
PID=$(pgrep multi_sensor_tool)
if [[ -n "$PID" ]]; then
    CPU_USAGE=$(ps -p "$PID" -o %cpu --no-headers | tr -d ' ')
    MEM_USAGE=$(ps -p "$PID" -o %mem --no-headers | tr -d ' ')

    # 发送到InfluxDB
    curl -i -XPOST "$INFLUXDB_URL/write?db=$DATABASE" \
        --data-binary "rtsp_tools,host=$(hostname) cpu_usage=$CPU_USAGE,mem_usage=$MEM_USAGE"
fi

# 检查网络连接
RTSP_CONNECTIONS=$(netstat -tn | grep :8554 | grep ESTABLISHED | wc -l)
curl -i -XPOST "$INFLUXDB_URL/write?db=$DATABASE" \
    --data-binary "rtsp_tools,host=$(hostname) rtsp_connections=$RTSP_CONNECTIONS"
EOF

sudo chmod +x /opt/rtsp_tools/bin/monitor.sh
```

### 高级配置

#### 1. 负载均衡配置
```bash
# 使用nginx进行RTSP负载均衡
sudo tee /etc/nginx/nginx.conf << 'EOF'
stream {
    upstream rtsp_backend {
        server ************:8554;
        server ************:8554;
        server ************:8554;
    }

    server {
        listen 8554;
        proxy_pass rtsp_backend;
        proxy_timeout 1s;
        proxy_responses 1;
    }
}
EOF
```

#### 2. 集群部署
```bash
# 创建集群配置文件
sudo tee /opt/rtsp_tools/configs/cluster.conf << 'EOF'
[cluster_node_1]
type=video,data=/shared/data/video1.ts,timestamp=/shared/data/video1.txt,output=rtsp://0.0.0.0:8554/stream1
type=radar,data=/shared/data/radar1.json,output=udp://*********:9001

[cluster_node_2]
type=video,data=/shared/data/video2.ts,timestamp=/shared/data/video2.txt,output=rtsp://0.0.0.0:8555/stream2
type=lidar,data=/shared/data/lidar1.json,output=udp://*********:9002
EOF
```

## 高级功能

### 1. 静态链接构建脚本

创建完全独立的可执行文件：

```bash
# 创建静态构建脚本
tee scripts/build_static.sh << 'EOF'
#!/bin/bash

set -e

echo "Building static linked executables..."

# 设置静态链接标志
export LDFLAGS="-static -static-libgcc -static-libstdc++"
export CXXFLAGS="-O3 -DNDEBUG -march=native"

# 构建静态版本
make clean
make STATIC=1 RELEASE=1 all

echo "Verifying static linking..."
for binary in build/bin/*; do
    if [[ -x "$binary" ]]; then
        echo "Checking $binary:"
        ldd "$binary" 2>&1 | head -3
        echo "Size: $(du -h "$binary" | cut -f1)"
        echo "---"
    fi
done

echo "Static build completed!"
EOF

chmod +x scripts/build_static.sh
```

### 2. 交叉编译支持

```bash
# 创建交叉编译脚本
tee scripts/cross_compile.sh << 'EOF'
#!/bin/bash

TARGET_ARCH=${1:-aarch64}
TARGET_OS=${2:-linux}

case "$TARGET_ARCH" in
    "aarch64")
        CROSS_PREFIX="aarch64-linux-gnu-"
        ;;
    "armv7")
        CROSS_PREFIX="arm-linux-gnueabihf-"
        ;;
    "x86_64")
        CROSS_PREFIX=""
        ;;
    *)
        echo "Unsupported architecture: $TARGET_ARCH"
        exit 1
        ;;
esac

export CC="${CROSS_PREFIX}gcc"
export CXX="${CROSS_PREFIX}g++"
export AR="${CROSS_PREFIX}ar"
export STRIP="${CROSS_PREFIX}strip"

echo "Cross compiling for $TARGET_ARCH-$TARGET_OS..."
make clean
make CROSS_COMPILE=1 all

echo "Cross compilation completed!"
EOF

chmod +x scripts/cross_compile.sh
```

### 3. 性能测试脚本

```bash
# 创建性能测试脚本
tee scripts/performance_test.sh << 'EOF'
#!/bin/bash

BINARY="./build/bin/multi_sensor_tool"
CONFIG="test/configs/test_config.txt"
GROUP="json-only-test"
DURATION=60

echo "Starting performance test..."
echo "Duration: ${DURATION} seconds"
echo "Binary: $BINARY"
echo "Config: $CONFIG"
echo "Group: $GROUP"

# 启动监控
pidstat -p $$ 1 > perf_cpu.log &
PIDSTAT_PID=$!

# 启动内存监控
while true; do
    ps -o pid,ppid,cmd,rss,vsz --sort=-rss -C multi_sensor_tool >> perf_memory.log
    sleep 1
done &
MEMORY_PID=$!

# 运行测试
timeout $DURATION $BINARY -f $CONFIG -g $GROUP -l -d > perf_output.log 2>&1

# 停止监控
kill $PIDSTAT_PID $MEMORY_PID 2>/dev/null

echo "Performance test completed!"
echo "Results saved to perf_*.log files"
EOF

chmod +x scripts/performance_test.sh
```

## 技术支持

### 问题报告

如遇到问题，请提供以下信息：

1. **系统信息**
   ```bash
   uname -a
   lsb_release -a  # Ubuntu/Debian
   cat /etc/redhat-release  # CentOS/RHEL
   ```

2. **编译器版本**
   ```bash
   gcc --version
   g++ --version
   ```

3. **依赖库版本**
   ```bash
   pkg-config --modversion libavformat libavcodec libavutil jsoncpp
   ```

4. **完整的错误日志**
   ```bash
   ./build/bin/multi_sensor_tool -f config.txt -g group -d > error.log 2>&1
   ```

5. **配置文件内容**（脱敏后）

### 常用调试命令

```bash
# 检查库依赖
ldd build/bin/multi_sensor_tool

# 检查符号表
nm build/bin/multi_sensor_tool | grep main

# 检查段信息
objdump -h build/bin/multi_sensor_tool

# 运行时调试
strace -e trace=network ./build/bin/multi_sensor_tool -f config.txt -g group

# 网络调试
ss -tulnp | grep -E "(8554|900[12])"
```

### 相关文档

- `docs/architecture/ARCHITECTURE_SIMPLE.md` - 架构设计文档
- `docs/user_guides/README_MULTI_SENSOR.md` - 多传感器使用指南
- `test/configs/` - 配置文件示例
- `PROJECT_STRUCTURE.md` - 项目结构说明

---

**文档版本**: v1.0
**最后更新**: 2025-08-07
**适用版本**: RTSP Tools v1.0+
