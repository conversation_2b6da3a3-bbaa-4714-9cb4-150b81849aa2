# RTSP工具使用示例

本文档提供了RTSP工具的详细使用示例，包括各种部署场景和配置方法。

## 快速开始

### 1. 基本编译和运行

```bash
# 克隆项目
git clone <repository-url>
cd rtsp_tools

# 安装依赖（Ubuntu/Debian）
sudo apt-get update
sudo apt-get install -y build-essential pkg-config
sudo apt-get install -y libavformat-dev libavcodec-dev libavutil-dev libjsoncpp-dev

# 编译所有工具
make all

# 运行多传感器工具
./build/bin/multi_sensor_tool -f test/configs/test_config.txt -g json-only-test -d
```

### 2. 创建便携式包

```bash
# 创建独立运行包
make package

# 解压并运行
tar -xzf rtsp_tools_portable_*.tar.gz
cd rtsp_tools_portable_*
./run.sh
```

### 3. 静态链接版本

```bash
# 编译静态链接版本（减少依赖）
make static

# 验证静态链接
ldd build/bin/multi_sensor_tool
```

## 配置文件示例

### 多传感器配置（新格式）

```ini
# sensors.conf
[vehicle_sensors]
# 前置摄像头
video:data/front_camera.ts,data/front_camera.txt,rtsp://*************:8554/front
# 鱼眼摄像头
fisheye:data/fisheye_camera.ts,data/fisheye_camera.txt,rtsp://*************:8554/fisheye
# 雷达数据
radar:data/radar_data.json,udp://*************:9001
# 激光雷达数据
lidar:data/lidar_data.json,udp://*************:9002

[test_sensors]
# 测试用传感器配置
radar:test_data/json/radar_test.json,udp://127.0.0.1:9001
lidar:test_data/json/lidar_test.json,udp://127.0.0.1:9002
```

### 传统RTSP配置（向后兼容）

```ini
# rtsp.conf
[camera_group_1]
video1.ts,video1.txt,rtsp://*************:8554/stream1
video2.ts,video2.txt,rtsp://*************:8554/stream2

[camera_group_2]
video3.ts,video3.txt,rtsp://************1:8554/stream1
video4.ts,video4.txt,rtsp://************1:8554/stream2
```

## 使用场景示例

### 场景1：自动驾驶数据回放

```bash
# 配置文件：autonomous_vehicle.conf
[highway_scene_001]
video:front_camera.ts,front_camera.txt,rtsp://*************:8554/front
video:rear_camera.ts,rear_camera.txt,rtsp://*************:8554/rear
fisheye:side_camera.ts,side_camera.txt,rtsp://*************:8554/side
radar:radar_objects.json,udp://*************:9001
lidar:lidar_points.json,udp://*************:9002

# 运行命令
./build/bin/multi_sensor_tool \
    -f autonomous_vehicle.conf \
    -g highway_scene_001 \
    -l \  # 循环播放
    -d    # 调试模式
```

### 场景2：监控系统测试

```bash
# 配置文件：surveillance.conf
[building_cameras]
video:entrance.ts,entrance.txt,rtsp://*************:8554/entrance
video:lobby.ts,lobby.txt,rtsp://*************:8554/lobby
video:parking.ts,parking.txt,rtsp://*************:8554/parking
fisheye:overview.ts,overview.txt,rtsp://*************:8554/overview

# 运行命令
./build/bin/multi_sensor_tool \
    -f surveillance.conf \
    -g building_cameras \
    -c 1  # 鱼眼摄像头类型
```

### 场景3：机器人传感器融合

```bash
# 配置文件：robot_sensors.conf
[robot_navigation]
video:rgb_camera.ts,rgb_camera.txt,rtsp://*************:8554/rgb
radar:obstacle_detection.json,udp://*************:9001
lidar:slam_data.json,udp://*************:9002

# 运行命令
./build/bin/multi_sensor_tool \
    -f robot_sensors.conf \
    -g robot_navigation \
    -l -d
```

## Docker部署示例

### 1. 构建Docker镜像

```bash
# 使用构建脚本
./scripts/build_docker.sh -n rtsp-tools -t v1.0

# 或手动构建
docker build -t rtsp-tools:v1.0 .
```

### 2. 运行Docker容器

```bash
# 基本运行
docker run --rm -it rtsp-tools:v1.0

# 暴露端口运行
docker run --rm \
    -p 8554:8554 \
    -p 9001:9001/udp \
    -p 9002:9002/udp \
    rtsp-tools:v1.0

# 挂载自定义配置和数据
docker run --rm \
    -v /path/to/configs:/opt/rtsp_tools/configs:ro \
    -v /path/to/data:/opt/rtsp_tools/data:ro \
    -p 8554:8554 \
    -p 9001:9001/udp \
    -p 9002:9002/udp \
    rtsp-tools:v1.0 \
    ./bin/multi_sensor_tool -f configs/my_config.txt -g my_group -l
```

### 3. Docker Compose部署

```yaml
# docker-compose.yml
version: '3.8'

services:
  rtsp-tools:
    image: rtsp-tools:v1.0
    container_name: rtsp-tools
    restart: unless-stopped
    ports:
      - "8554:8554"
      - "9001:9001/udp"
      - "9002:9002/udp"
    volumes:
      - ./configs:/opt/rtsp_tools/configs:ro
      - ./data:/opt/rtsp_tools/data:ro
      - ./logs:/opt/rtsp_tools/logs
    environment:
      - RTSP_LOG_LEVEL=INFO
      - RTSP_TIMEOUT=30
    command: >
      ./bin/multi_sensor_tool
      -f configs/production.conf
      -g main_sensors
      -l
```

```bash
# 启动服务
docker-compose up -d

# 查看日志
docker-compose logs -f

# 停止服务
docker-compose down
```

## 生产环境部署

### 1. systemd服务配置

```bash
# 创建服务文件
sudo tee /etc/systemd/system/rtsp-tools.service << 'EOF'
[Unit]
Description=RTSP Multi-Sensor Tools
After=network.target
Wants=network.target

[Service]
Type=simple
User=rtsp_user
Group=rtsp_user
WorkingDirectory=/opt/rtsp_tools
ExecStart=/opt/rtsp_tools/bin/multi_sensor_tool -f /opt/rtsp_tools/configs/production.conf -g production -l
ExecReload=/bin/kill -HUP $MAINPID
Restart=always
RestartSec=5
StandardOutput=journal
StandardError=journal

# 安全设置
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=/opt/rtsp_tools /var/log/rtsp_tools

# 资源限制
LimitNOFILE=65536
LimitNPROC=4096

[Install]
WantedBy=multi-user.target
EOF

# 启用并启动服务
sudo systemctl daemon-reload
sudo systemctl enable rtsp-tools.service
sudo systemctl start rtsp-tools.service

# 检查状态
sudo systemctl status rtsp-tools.service
```

### 2. 负载均衡配置

```nginx
# /etc/nginx/nginx.conf
stream {
    upstream rtsp_backend {
        server ************:8554 weight=3;
        server ************:8554 weight=2;
        server ************:8554 weight=1;
    }
    
    server {
        listen 8554;
        proxy_pass rtsp_backend;
        proxy_timeout 1s;
        proxy_responses 1;
        proxy_bind $remote_addr transparent;
    }
}
```

### 3. 监控配置

```bash
# Prometheus监控配置
# /etc/prometheus/prometheus.yml
scrape_configs:
  - job_name: 'rtsp-tools'
    static_configs:
      - targets: ['localhost:9090']
    metrics_path: /metrics
    scrape_interval: 15s
```

## 性能调优示例

### 1. 系统级优化

```bash
# 网络优化
echo 'net.core.rmem_max = 134217728' | sudo tee -a /etc/sysctl.conf
echo 'net.core.wmem_max = 134217728' | sudo tee -a /etc/sysctl.conf
echo 'net.core.netdev_max_backlog = 5000' | sudo tee -a /etc/sysctl.conf
sudo sysctl -p

# 进程优化
nice -n -10 ./build/bin/multi_sensor_tool -f config.txt -g sensors
taskset -c 0,1 ./build/bin/multi_sensor_tool -f config.txt -g sensors
```

### 2. 编译优化

```bash
# 高性能编译
make RELEASE=1 LTO=1 NATIVE=1 all

# 自定义优化标志
make CXXFLAGS="-O3 -march=native -mtune=native -flto" all
```

## 故障排除示例

### 1. 网络连接问题

```bash
# 检查端口占用
netstat -tulnp | grep -E "(8554|900[12])"

# 测试UDP连接
nc -u -l 9001 &
echo "test" | nc -u 127.0.0.1 9001

# 测试RTSP连接
telnet ************* 8554
```

### 2. 调试和日志

```bash
# 启用详细调试
./build/bin/multi_sensor_tool -f config.txt -g sensors -d > debug.log 2>&1

# 使用strace跟踪系统调用
strace -e trace=network ./build/bin/multi_sensor_tool -f config.txt -g sensors

# 内存调试
valgrind --leak-check=full ./build/bin/multi_sensor_tool -f config.txt -g sensors
```

### 3. 性能分析

```bash
# CPU性能分析
perf record ./build/bin/multi_sensor_tool -f config.txt -g sensors
perf report

# 实时监控
top -p $(pgrep multi_sensor_tool)
htop -p $(pgrep multi_sensor_tool)
```

## 集成示例

### 1. 与FFmpeg集成

```bash
# 从RTSP流录制
ffmpeg -i rtsp://*************:8554/stream1 -c copy output.mp4

# 转换格式
ffmpeg -i input.mp4 -c:v libx264 -c:a aac output.ts
```

### 2. 与Python集成

```python
import subprocess
import json
import socket

# 启动RTSP工具
process = subprocess.Popen([
    './build/bin/multi_sensor_tool',
    '-f', 'config.txt',
    '-g', 'sensors',
    '-d'
], stdout=subprocess.PIPE, stderr=subprocess.PIPE)

# 监听UDP数据
sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
sock.bind(('127.0.0.1', 9001))

while True:
    data, addr = sock.recvfrom(1024)
    sensor_data = json.loads(data.decode())
    print(f"Received sensor data: {sensor_data}")
```

这些示例涵盖了RTSP工具的主要使用场景，从基本的编译运行到复杂的生产环境部署。根据具体需求选择合适的配置和部署方式。
