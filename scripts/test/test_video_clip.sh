#!/bin/bash

# 视频剪辑工具测试脚本
# 用于测试 video_clip_tool 的功能

set -e

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${GREEN}========================================${NC}"
echo -e "${GREEN}     视频剪辑工具测试脚本${NC}"
echo -e "${GREEN}========================================${NC}"

# 检查工具是否已编译
TOOL_PATH="../../build/bin/video_clip_tool"
if [ ! -f "$TOOL_PATH" ]; then
    echo -e "${YELLOW}工具未编译，正在编译...${NC}"
    cd ../..
    make original
    cd scripts/test
fi

if [ ! -f "$TOOL_PATH" ]; then
    echo -e "${RED}错误：编译失败，无法找到 video_clip_tool${NC}"
    exit 1
fi

# 测试数据目录
TEST_DATA_DIR="../../test/data/video"
OUTPUT_DIR="../../test/output"

# 创建输出目录
mkdir -p "$OUTPUT_DIR"

echo -e "\n${GREEN}测试文件列表：${NC}"
ls -la "$TEST_DATA_DIR"/*.ts 2>/dev/null || echo "未找到测试视频文件"

# 选择第一个测试视频
TEST_VIDEO=$(ls "$TEST_DATA_DIR"/*.ts 2>/dev/null | head -n 1)
TEST_TIMESTAMP_FILE="${TEST_VIDEO%.ts}.txt"

if [ -z "$TEST_VIDEO" ]; then
    echo -e "${RED}错误：未找到测试视频文件${NC}"
    echo "请在 $TEST_DATA_DIR 目录下放置 .ts 格式的测试视频"
    exit 1
fi

echo -e "\n${GREEN}使用测试视频：${NC}$TEST_VIDEO"

# 检查时间戳文件
if [ -f "$TEST_TIMESTAMP_FILE" ]; then
    echo -e "${GREEN}找到时间戳文件：${NC}$TEST_TIMESTAMP_FILE"
    echo "时间戳内容（前5行）："
    head -n 5 "$TEST_TIMESTAMP_FILE"
    
    # 读取时间戳范围
    FIRST_TS=$(head -n 1 "$TEST_TIMESTAMP_FILE")
    LAST_TS=$(tail -n 1 "$TEST_TIMESTAMP_FILE")
    
    # 计算测试时间范围
    if [ ! -z "$FIRST_TS" ] && [ ! -z "$LAST_TS" ]; then
        # 从第一个时间戳开始，剪辑2秒
        START_TIME=$FIRST_TS
        END_TIME=$((FIRST_TS + 2000))
        
        echo -e "\n${YELLOW}测试1：剪辑前2秒视频${NC}"
        echo "开始时间：$START_TIME ms"
        echo "结束时间：$END_TIME ms"
        
        OUTPUT_FILE="$OUTPUT_DIR/clip_2sec.ts"
        echo -e "\n执行命令："
        echo "$TOOL_PATH \"$TEST_VIDEO\" \"$OUTPUT_FILE\" $START_TIME $END_TIME"
        
        if $TOOL_PATH "$TEST_VIDEO" "$OUTPUT_FILE" $START_TIME $END_TIME; then
            echo -e "${GREEN}✓ 剪辑成功！${NC}"
            echo "输出文件：$OUTPUT_FILE"
            ls -lh "$OUTPUT_FILE" 2>/dev/null
        else
            echo -e "${RED}✗ 剪辑失败${NC}"
        fi
    fi
else
    echo -e "${YELLOW}未找到时间戳文件，使用默认时间范围测试${NC}"
    
    # 使用相对时间测试（假设视频至少有5秒）
    echo -e "\n${YELLOW}测试2：使用相对时间剪辑（0-3秒）${NC}"
    
    OUTPUT_FILE="$OUTPUT_DIR/clip_relative.ts"
    echo -e "\n执行命令："
    echo "$TOOL_PATH \"$TEST_VIDEO\" \"$OUTPUT_FILE\" \"00:00:00.000\" \"00:00:03.000\""
    
    if $TOOL_PATH "$TEST_VIDEO" "$OUTPUT_FILE" "00:00:00.000" "00:00:03.000"; then
        echo -e "${GREEN}✓ 剪辑成功！${NC}"
        echo "输出文件：$OUTPUT_FILE"
        ls -lh "$OUTPUT_FILE" 2>/dev/null
    else
        echo -e "${YELLOW}提示：相对时间格式可能不支持，请使用毫秒时间戳${NC}"
    fi
fi

# 测试错误处理
echo -e "\n${YELLOW}测试3：错误处理测试${NC}"

echo -e "\n测试无效时间范围（结束时间小于开始时间）："
OUTPUT_FILE="$OUTPUT_DIR/clip_error.ts"
if ! $TOOL_PATH "$TEST_VIDEO" "$OUTPUT_FILE" 2000 1000 2>/dev/null; then
    echo -e "${GREEN}✓ 正确处理了无效时间范围${NC}"
else
    echo -e "${RED}✗ 未能正确处理无效时间范围${NC}"
fi

echo -e "\n${GREEN}========================================${NC}"
echo -e "${GREEN}测试完成！${NC}"
echo -e "\n输出文件在：$OUTPUT_DIR"
ls -lh "$OUTPUT_DIR"/*.ts 2>/dev/null || echo "未生成输出文件"

echo -e "\n${YELLOW}提示：${NC}"
echo "1. 可以使用 ffplay 或 VLC 播放剪辑后的视频文件"
echo "2. 可以使用 rtsp_timestamp_proc_tool 提取剪辑后视频的时间戳验证"
echo "3. 示例命令："
echo "   ffplay $OUTPUT_DIR/clip_2sec.ts"
echo "   ../../build/bin/rtsp_timestamp_proc_tool $OUTPUT_DIR/clip_2sec.ts"
