#!/bin/bash

# 批量视频剪辑脚本
# 用于批量处理指定目录下的 TS 视频文件，剪辑指定时间范围并生成时间戳文件
# 
# 使用方法:
#   ./batch_clip_videos.sh <输入目录> <输出目录> <开始时间> <结束时间> [选项]
#
# 时间格式支持:
#   1. 毫秒时间戳: 1723169047372
#   2. 相对偏移(秒): +5 (从视频开始后5秒)
#   3. 相对时长(秒): +5:10 (从开始后5秒，持续10秒)

set -e

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 默认配置
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/.." && pwd)"
VIDEO_CLIP_TOOL="$PROJECT_ROOT/build/bin/video_clip_tool"
TIMESTAMP_TOOL="$PROJECT_ROOT/build/bin/rtsp_timestamp_proc_tool"

# 统计变量
TOTAL_FILES=0
SUCCESS_COUNT=0
FAILED_COUNT=0
SKIPPED_COUNT=0

# 显示使用帮助
show_help() {
    echo -e "${GREEN}批量视频剪辑工具${NC}"
    echo ""
    echo "用法:"
    echo "  $0 <输入目录> <输出目录> <开始时间> <结束时间> [选项]"
    echo ""
    echo "时间格式:"
    echo "  1. 毫秒时间戳     : 1723169047372 1723169050000"
    echo "  2. ISO时间格式    : \"2024-08-09T10:04:10.000\" \"2024-08-09T10:04:20.000\""
    echo "  3. 相对偏移(秒)   : +5 +15 (从开始后5秒到15秒)"
    echo "  4. 相对时长(秒)   : +5:10 (从开始后5秒，持续10秒)"
    echo "  5. 固定时长(秒)   : 0:30 (从开始剪辑30秒)"
    echo ""
    echo "选项:"
    echo "  -h, --help        显示此帮助信息"
    echo "  -f, --force       覆盖已存在的输出文件"
    echo "  -v, --verbose     显示详细输出"
    echo "  -p, --prefix      输出文件前缀 (默认: clip_)"
    echo "  -s, --suffix      输出文件后缀 (默认: 无)"
    echo "  --no-timestamp    不生成时间戳文件"
    echo ""
    echo "示例:"
    echo "  # 使用毫秒时间戳剪辑"
    echo "  $0 /path/to/videos /path/to/output 1723169047372 1723169050000"
    echo ""
    echo "  # 使用ISO时间格式剪辑"
    echo "  $0 /path/to/videos /path/to/output \"2024-08-09T10:04:10.000\" \"2024-08-09T10:04:20.000\""
    echo ""
    echo "  # 剪辑每个视频的前30秒"
    echo "  $0 /path/to/videos /path/to/output 0:30"
    echo ""
    echo "  # 从第5秒开始剪辑10秒"
    echo "  $0 /path/to/videos /path/to/output +5:10"
    echo ""
    echo "  # 从第5秒剪辑到第15秒"
    echo "  $0 /path/to/videos /path/to/output +5 +15 --prefix segment_"
}

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查工具是否存在
check_tools() {
    if [ ! -f "$VIDEO_CLIP_TOOL" ]; then
        log_error "视频剪辑工具未找到: $VIDEO_CLIP_TOOL"
        log_info "请先编译项目: make original"
        exit 1
    fi
    
    if [ ! -f "$TIMESTAMP_TOOL" ]; then
        log_error "时间戳工具未找到: $TIMESTAMP_TOOL"
        log_info "请先编译项目: make original"
        exit 1
    fi
    
    log_info "工具检查通过"
}

# 解析时间参数
parse_time_params() {
    local input_dir="$1"
    local start_time="$2"
    local end_time="$3"
    
    # 如果只有一个时间参数，解析为时长格式
    if [ -z "$end_time" ]; then
        if [[ "$start_time" =~ ^([0-9]+):([0-9]+)$ ]]; then
            # 格式: 0:30 (从0秒开始，持续30秒)
            TIME_MODE="duration"
            DURATION_SECONDS="${BASH_REMATCH[2]}"
            OFFSET_SECONDS="${BASH_REMATCH[1]}"
            log_info "时间模式: 固定时长 - 偏移${OFFSET_SECONDS}秒，持续${DURATION_SECONDS}秒"
        elif [[ "$start_time" =~ ^\+([0-9]+):([0-9]+)$ ]]; then
            # 格式: +5:10 (从第5秒开始，持续10秒)
            TIME_MODE="offset_duration"
            OFFSET_SECONDS="${BASH_REMATCH[1]}"
            DURATION_SECONDS="${BASH_REMATCH[2]}"
            log_info "时间模式: 相对时长 - 偏移${OFFSET_SECONDS}秒，持续${DURATION_SECONDS}秒"
        else
            log_error "无效的时间格式: $start_time"
            show_help
            exit 1
        fi
    else
        # 两个参数的情况
        if [[ "$start_time" =~ ^\+([0-9]+)$ ]] && [[ "$end_time" =~ ^\+([0-9]+)$ ]]; then
            # 格式: +5 +15 (从第5秒到第15秒)
            TIME_MODE="relative_range"
            OFFSET_START="${BASH_REMATCH[1]}"
            OFFSET_END="${end_time:1}"
            log_info "时间模式: 相对范围 - 第${OFFSET_START}秒到第${OFFSET_END}秒"
        elif [[ "$start_time" =~ ^[0-9]+$ ]] && [[ "$end_time" =~ ^[0-9]+$ ]]; then
            # 格式: 1723169047372 1723169050000 (毫秒时间戳)
            TIME_MODE="timestamp"
            TIMESTAMP_START="$start_time"
            TIMESTAMP_END="$end_time"
            log_info "时间模式: 绝对时间戳 - ${TIMESTAMP_START}ms 到 ${TIMESTAMP_END}ms"
        elif [[ "$start_time" =~ ^[0-9]{4}-[0-9]{2}-[0-9]{2}T[0-9]{2}:[0-9]{2}:[0-9]{2} ]] && [[ "$end_time" =~ ^[0-9]{4}-[0-9]{2}-[0-9]{2}T[0-9]{2}:[0-9]{2}:[0-9]{2} ]]; then
            # 格式: ISO 时间格式
            TIME_MODE="iso"
            ISO_START="$start_time"
            ISO_END="$end_time"
            log_info "时间模式: ISO时间格式 - ${ISO_START} 到 ${ISO_END}"
            
            # 转换为毫秒时间戳
            log_info "转换ISO时间为毫秒时间戳..."
            TIMESTAMP_START=$(date -d "$ISO_START" +%s%3N 2>/dev/null || echo "-1")
            TIMESTAMP_END=$(date -d "$ISO_END" +%s%3N 2>/dev/null || echo "-1")
            
            if [ "$TIMESTAMP_START" = "-1" ] || [ "$TIMESTAMP_END" = "-1" ]; then
                log_error "ISO时间格式转换失败"
                log_error "开始时间: $ISO_START -> $TIMESTAMP_START"
                log_error "结束时间: $ISO_END -> $TIMESTAMP_END"
                exit 1
            fi
            
            log_info "ISO时间转换成功:"
            log_info "  开始: $ISO_START -> ${TIMESTAMP_START}ms"
            log_info "  结束: $ISO_END -> ${TIMESTAMP_END}ms"
            
            # 重新设置为时间戳模式
            TIME_MODE="timestamp"
        else
            log_error "无效的时间格式: $start_time $end_time"
            show_help
            exit 1
        fi
    fi
}

# 计算剪辑时间
calculate_clip_time() {
    local video_file="$1"
    local timestamp_file="${video_file%.ts}.txt"
    
    case "$TIME_MODE" in
        "duration"|"offset_duration")
            # 需要读取视频的时间戳
            if [ ! -f "$timestamp_file" ]; then
                log_info "提取时间戳: $(basename "$video_file")"
                if ! "$TIMESTAMP_TOOL" "$video_file" > /dev/null 2>&1; then
                    log_error "时间戳提取失败: $video_file"
                    return 1
                fi
            fi
            
            if [ ! -f "$timestamp_file" ] || [ ! -s "$timestamp_file" ]; then
                log_error "时间戳文件为空或不存在: $timestamp_file"
                return 1
            fi
            
            local first_ts=$(head -n 1 "$timestamp_file")
            if [ -z "$first_ts" ]; then
                log_error "无法读取时间戳: $timestamp_file"
                return 1
            fi
            
            if [ "$TIME_MODE" = "duration" ]; then
                CLIP_START=$((first_ts + OFFSET_SECONDS * 1000))
                CLIP_END=$((first_ts + (OFFSET_SECONDS + DURATION_SECONDS) * 1000))
            else  # offset_duration
                CLIP_START=$((first_ts + OFFSET_SECONDS * 1000))
                CLIP_END=$((first_ts + (OFFSET_SECONDS + DURATION_SECONDS) * 1000))
            fi
            ;;
            
        "relative_range")
            # 需要读取视频的时间戳
            if [ ! -f "$timestamp_file" ]; then
                log_info "提取时间戳: $(basename "$video_file")"
                if ! "$TIMESTAMP_TOOL" "$video_file" > /dev/null 2>&1; then
                    log_error "时间戳提取失败: $video_file"
                    return 1
                fi
            fi
            
            if [ ! -f "$timestamp_file" ] || [ ! -s "$timestamp_file" ]; then
                log_error "时间戳文件为空或不存在: $timestamp_file"
                return 1
            fi
            
            local first_ts=$(head -n 1 "$timestamp_file")
            if [ -z "$first_ts" ]; then
                log_error "无法读取时间戳: $timestamp_file"
                return 1
            fi
            
            CLIP_START=$((first_ts + OFFSET_START * 1000))
            CLIP_END=$((first_ts + OFFSET_END * 1000))
            ;;
            
        "timestamp")
            CLIP_START="$TIMESTAMP_START"
            CLIP_END="$TIMESTAMP_END"
            ;;
    esac
    
    return 0
}

# 处理单个视频文件
process_video() {
    local video_file="$1"
    local output_dir="$2"
    local filename=$(basename "$video_file")
    local name_without_ext="${filename%.ts}"
    
    # 生成输出文件名
    local output_video="$output_dir/${OUTPUT_PREFIX}${name_without_ext}${OUTPUT_SUFFIX}.ts"
    local output_timestamp="$output_dir/${OUTPUT_PREFIX}${name_without_ext}${OUTPUT_SUFFIX}.txt"
    
    log_info "处理视频: $filename"
    
    # 检查是否需要跳过
    if [ "$FORCE_OVERWRITE" != "true" ] && [ -f "$output_video" ]; then
        log_warning "输出文件已存在，跳过: $(basename "$output_video")"
        SKIPPED_COUNT=$((SKIPPED_COUNT + 1))
        return 0
    fi
    
    # 计算剪辑时间
    if ! calculate_clip_time "$video_file"; then
        log_error "时间计算失败: $filename"
        FAILED_COUNT=$((FAILED_COUNT + 1))
        return 1
    fi
    
    if [ "$VERBOSE" = "true" ]; then
        log_info "剪辑时间: ${CLIP_START}ms - ${CLIP_END}ms"
        log_info "输出文件: $(basename "$output_video")"
    fi
    
    # 执行剪辑
    if [ "$VERBOSE" = "true" ]; then
        "$VIDEO_CLIP_TOOL" "$video_file" "$output_video" "$CLIP_START" "$CLIP_END"
    else
        "$VIDEO_CLIP_TOOL" "$video_file" "$output_video" "$CLIP_START" "$CLIP_END" > /dev/null 2>&1
    fi
    
    if [ $? -eq 0 ]; then
        log_success "剪辑完成: $(basename "$output_video")"
        
        # 生成时间戳文件
        if [ "$GENERATE_TIMESTAMP" = "true" ]; then
            if [ "$VERBOSE" = "true" ]; then
                log_info "生成时间戳文件: $(basename "$output_timestamp")"
                "$TIMESTAMP_TOOL" "$output_video" "$output_timestamp"
            else
                "$TIMESTAMP_TOOL" "$output_video" "$output_timestamp" > /dev/null 2>&1
            fi
            
            if [ $? -eq 0 ]; then
                log_success "时间戳文件生成完成: $(basename "$output_timestamp")"
            else
                log_warning "时间戳文件生成失败: $(basename "$output_timestamp")"
            fi
        fi
        
        SUCCESS_COUNT=$((SUCCESS_COUNT + 1))
    else
        log_error "剪辑失败: $filename"
        FAILED_COUNT=$((FAILED_COUNT + 1))
        return 1
    fi
    
    return 0
}

# 主函数
main() {
    # 默认选项
    FORCE_OVERWRITE="false"
    VERBOSE="false"
    OUTPUT_PREFIX="clip_"
    OUTPUT_SUFFIX=""
    GENERATE_TIMESTAMP="true"
    
    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            -f|--force)
                FORCE_OVERWRITE="true"
                shift
                ;;
            -v|--verbose)
                VERBOSE="true"
                shift
                ;;
            -p|--prefix)
                OUTPUT_PREFIX="$2"
                shift 2
                ;;
            -s|--suffix)
                OUTPUT_SUFFIX="$2"
                shift 2
                ;;
            --no-timestamp)
                GENERATE_TIMESTAMP="false"
                shift
                ;;
            -*)
                log_error "未知选项: $1"
                show_help
                exit 1
                ;;
            *)
                break
                ;;
        esac
    done
    
    # 检查必需参数
    if [ $# -lt 3 ]; then
        log_error "参数不足"
        show_help
        exit 1
    fi
    
    INPUT_DIR="$1"
    OUTPUT_DIR="$2"
    START_TIME="$3"
    END_TIME="$4"
    
    # 验证输入目录
    if [ ! -d "$INPUT_DIR" ]; then
        log_error "输入目录不存在: $INPUT_DIR"
        exit 1
    fi
    
    # 创建输出目录
    if [ ! -d "$OUTPUT_DIR" ]; then
        log_info "创建输出目录: $OUTPUT_DIR"
        mkdir -p "$OUTPUT_DIR"
    fi
    
    # 检查工具
    check_tools
    
    # 解析时间参数
    parse_time_params "$INPUT_DIR" "$START_TIME" "$END_TIME"
    
    # 查找 TS 文件
    log_info "扫描目录: $INPUT_DIR"
    
    # 使用 find 命令查找所有 .ts 文件
    while IFS= read -r -d '' video_file; do
        TOTAL_FILES=$((TOTAL_FILES + 1))
        
        if [ "$VERBOSE" = "true" ]; then
            echo ""
            log_info "=== 处理第 $TOTAL_FILES 个文件 ==="
        fi
        
        process_video "$video_file" "$OUTPUT_DIR"
    done < <(find "$INPUT_DIR" -name "*.ts" -type f -print0)
    
    # 显示统计结果
    echo ""
    echo -e "${GREEN}========================================${NC}"
    echo -e "${GREEN}           批量剪辑完成${NC}"
    echo -e "${GREEN}========================================${NC}"
    echo "总文件数: $TOTAL_FILES"
    echo -e "成功处理: ${GREEN}$SUCCESS_COUNT${NC}"
    echo -e "处理失败: ${RED}$FAILED_COUNT${NC}"
    echo -e "跳过文件: ${YELLOW}$SKIPPED_COUNT${NC}"
    echo ""
    echo "输出目录: $OUTPUT_DIR"
    
    if [ $TOTAL_FILES -eq 0 ]; then
        log_warning "未找到 TS 文件"
        exit 1
    fi
    
    if [ $SUCCESS_COUNT -gt 0 ]; then
        log_success "批量处理完成！"
        ls -lh "$OUTPUT_DIR"/*.ts 2>/dev/null | head -5
        if [ $(ls "$OUTPUT_DIR"/*.ts 2>/dev/null | wc -l) -gt 5 ]; then
            echo "... (显示前5个文件)"
        fi
    fi
    
    exit 0
}

# 脚本入口
main "$@"
